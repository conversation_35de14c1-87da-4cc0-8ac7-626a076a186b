"""
Reverie Code Studio (RCS) - AI IDE Server
Main FastAPI application entry point
"""

import argparse
import asyncio
import json
import os
import sys
import socket
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.api import chat, agent, files, models
from app.core.config import settings
from app.core.websocket_manager import WebSocketManager
try:
    from app.services.enhanced_ai_service import EnhancedAIService
    ENHANCED_MODE = True
except ImportError:
    from app.services.simple_enhanced_ai_service import SimpleEnhancedAIService as EnhancedAIService
    ENHANCED_MODE = False

# Initialize services (will be used in lifespan)
websocket_manager = WebSocketManager()
ai_service = EnhancedAIService()

def get_local_ip():
    """Get local IP address"""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"


def get_client_connection_ips():
    """Get all IP addresses that clients can use to connect"""
    connection_ips = []

    try:
        # Get local network IP
        local_ip = get_local_ip()
        if local_ip != "127.0.0.1":
            connection_ips.append(local_ip)

        # Always include localhost
        connection_ips.append("127.0.0.1")

        # Get all network interfaces
        hostname = socket.gethostname()
        for info in socket.getaddrinfo(hostname, None):
            ip = info[4][0]
            # Filter out IPv6 and loopback addresses
            if ":" not in ip and ip not in connection_ips and not ip.startswith("169.254"):
                connection_ips.append(ip)

    except Exception as e:
        logger.warning(f"Could not detect all network IPs: {e}")
        if "127.0.0.1" not in connection_ips:
            connection_ips.append("127.0.0.1")

    return connection_ips


def get_default_config() -> Dict[str, Any]:
    """Get default configuration"""
    return {
        "server": {
            "host": "0.0.0.0",
            "port": 27027,
            "debug": True,
            "log_level": "INFO"
        },
        "ai": {
            "default_model": "Qwen/Qwen1.5-4B",
            "model_path": "",
            "device": "auto",
            "max_length": 32768,
            "temperature": 0.7,
            "openai_api_key": "",
            "openai_model": ""
        },
        "features": {
            "web_search": False,
            "search_api_key": "",
            "file_operations": True,
            "code_completion": True,
            "agent_mode": True,
            "chat_mode": True
        },
        "security": {
            "cors_origins": ["*"],
            "max_file_size": 10485760,
            "allowed_extensions": [".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", ".json", ".md", ".txt"]
        },
        "storage": {
            "data_dir": "./data",
            "models_dir": "./data/models",
            "conversations_dir": "./data/conversations",
            "uploads_dir": "./data/uploads"
        }
    }


def load_config() -> Dict[str, Any]:
    """Load configuration from config.json or create default"""
    config_file = Path("config.json")

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info("📄 Loaded configuration from config.json")
            return config
        except Exception as e:
            logger.warning(f"⚠️ Failed to load config.json: {e}")
            logger.info("📄 Using default configuration")

    # Create default config
    config = get_default_config()
    save_config(config)
    return config


def save_config(config: Dict[str, Any]):
    """Save configuration to config.json"""
    try:
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        logger.info("💾 Configuration saved to config.json")
    except Exception as e:
        logger.error(f"❌ Failed to save config.json: {e}")


def apply_config_to_settings(config: Dict[str, Any]):
    """Apply configuration to settings"""
    try:
        # Server settings
        server_config = config.get("server", {})
        settings.HOST = server_config.get("host", settings.HOST)
        settings.PORT = server_config.get("port", settings.PORT)
        settings.DEBUG = server_config.get("debug", settings.DEBUG)

        # AI settings
        ai_config = config.get("ai", {})
        settings.MODEL_NAME = ai_config.get("default_model", settings.MODEL_NAME)
        settings.MODEL_PATH = ai_config.get("model_path", settings.MODEL_PATH)
        settings.DEVICE = ai_config.get("device", settings.DEVICE)
        settings.MAX_LENGTH = ai_config.get("max_length", settings.MAX_LENGTH)
        settings.TEMPERATURE = ai_config.get("temperature", settings.TEMPERATURE)
        settings.OPENAI_API_KEY = ai_config.get("openai_api_key", settings.OPENAI_API_KEY)
        settings.OPENAI_MODEL = ai_config.get("openai_model", settings.OPENAI_MODEL)

        # Features
        features_config = config.get("features", {})
        settings.SEARCH_API_KEY = features_config.get("search_api_key", settings.SEARCH_API_KEY)

        # Security
        security_config = config.get("security", {})
        settings.MAX_FILE_SIZE = security_config.get("max_file_size", settings.MAX_FILE_SIZE)

        logger.info("⚙️ Configuration applied to settings")

    except Exception as e:
        logger.error(f"❌ Failed to apply configuration: {e}")


def get_all_ips():
    """Get all available IP addresses"""
    ips = []
    try:
        hostname = socket.gethostname()
        ips.append(f"Hostname: {hostname}")

        # Get all IP addresses
        for info in socket.getaddrinfo(hostname, None):
            ip = info[4][0]
            if ip not in [item.split(": ")[1] for item in ips if ": " in item]:
                ips.append(f"IP: {ip}")
    except Exception:
        pass

    # Always include localhost
    if "IP: 127.0.0.1" not in ips:
        ips.append("IP: 127.0.0.1")

    return ips

# App configuration will be done after lifespan definition


from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Reverie Code Studio Server...")

    # Initialize AI service
    try:
        await ai_service.initialize()
        logger.info("AI Service initialized")
    except Exception as e:
        logger.error(f"Failed to initialize AI service: {e}")

    # Create necessary directories
    os.makedirs("data/conversations", exist_ok=True)
    os.makedirs("data/projects", exist_ok=True)
    os.makedirs("data/temp", exist_ok=True)
    os.makedirs("data/uploads", exist_ok=True)
    os.makedirs("static", exist_ok=True)

    # Initialize and start integrated CLI
    try:
        await integrated_cli.initialize()
        # Start CLI input thread
        integrated_cli.start_input_thread()
        logger.info("Integrated CLI started")
    except Exception as e:
        logger.warning(f"Failed to start CLI: {e}")

    logger.info("Server startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Reverie Code Studio Server...")
    try:
        # Stop CLI
        integrated_cli.running = False
        if integrated_cli.input_thread and integrated_cli.input_thread.is_alive():
            integrated_cli.input_thread.join(timeout=1.0)

        await ai_service.cleanup()
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="Rilance Code Studio API",
    description="AI-powered IDE server with chat, agent, and code completion capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# Initialize services
ai_service = EnhancedAIService()
websocket_manager = WebSocketManager()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add access logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests with IP, timestamp, and endpoint"""
    start_time = datetime.now()

    # Get client IP
    client_ip = request.client.host if request.client else "unknown"
    if "x-forwarded-for" in request.headers:
        client_ip = request.headers["x-forwarded-for"].split(",")[0].strip()
    elif "x-real-ip" in request.headers:
        client_ip = request.headers["x-real-ip"]

    # Process request
    response = await call_next(request)

    # Calculate processing time
    process_time = (datetime.now() - start_time).total_seconds()

    # Log request
    logger.info(
        f"ACCESS | {client_ip} | {request.method} {request.url.path} | "
        f"Status: {response.status_code} | Time: {process_time:.3f}s | "
        f"User-Agent: {request.headers.get('user-agent', 'unknown')[:50]}"
    )

    return response

# Mount static files
import os
static_dir = os.path.join(os.path.dirname(__file__), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Include API routers
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(agent.router, prefix="/api/agent", tags=["agent"])
app.include_router(files.router, prefix="/api/files", tags=["files"])
app.include_router(models.router, prefix="/api/models", tags=["models"])


@app.get("/")
async def root():
    """Redirect to documentation page"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/static/index.html")

@app.get("/docs")
async def docs_redirect():
    """Redirect to API documentation"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/static/api-docs.html")

@app.get("/api/docs")
async def api_docs():
    """API Documentation Service"""
    config_host = settings.HOST if settings.HOST != "0.0.0.0" else get_local_ip()

    return {
        "title": "Rilance Code Studio API Documentation",
        "version": "1.0.0",
        "description": "Complete API reference for RCS server",
        "server_info": {
            "host": config_host,
            "port": settings.PORT,
            "base_url": f"http://{config_host}:{settings.PORT}"
        },
        "endpoints": {
            "health": {
                "method": "GET",
                "path": "/health",
                "description": "Server health check and status",
                "example": f"http://{config_host}:{settings.PORT}/health"
            },
            "api_info": {
                "method": "GET",
                "path": "/api",
                "description": "API information and endpoints",
                "example": f"http://{config_host}:{settings.PORT}/api"
            },
            "chat": {
                "method": "POST",
                "path": "/api/chat/message",
                "description": "Send message to AI chat service",
                "example": f"http://{config_host}:{settings.PORT}/api/chat/message",
                "body": {
                    "message": "Hello, how can you help me?",
                    "conversation_id": "optional_id",
                    "context": {}
                }
            },
            "agent": {
                "method": "POST",
                "path": "/api/agent/task",
                "description": "Start AI agent task",
                "example": f"http://{config_host}:{settings.PORT}/api/agent/task",
                "body": {
                    "task": "Create a Python function to calculate fibonacci",
                    "project_path": "/path/to/project",
                    "context": {}
                }
            },
            "files": {
                "read": {
                    "method": "GET",
                    "path": "/api/files/read",
                    "description": "Read file content",
                    "params": {"file_path": "path/to/file.py"}
                },
                "write": {
                    "method": "POST",
                    "path": "/api/files/write",
                    "description": "Write file content",
                    "body": {
                        "file_path": "path/to/file.py",
                        "content": "file content"
                    }
                }
            },
            "models": {
                "list": {
                    "method": "GET",
                    "path": "/api/models/list",
                    "description": "List available AI models"
                },
                "load": {
                    "method": "POST",
                    "path": "/api/models/load",
                    "description": "Load specific AI model",
                    "body": {"model_name": "model_name"}
                }
            },
            "websocket": {
                "protocol": "WebSocket",
                "path": "/ws/{client_id}",
                "description": "Real-time communication",
                "example": f"ws://{config_host}:{settings.PORT}/ws/client123"
            },
            "cli": {
                "execute": {
                    "method": "POST",
                    "path": "/api/cli",
                    "description": "Execute CLI commands",
                    "body": {"command": "models list"}
                },
                "help": {
                    "method": "GET",
                    "path": "/api/cli/help",
                    "description": "Get CLI help and available commands"
                }
            }
        },
        "authentication": {
            "type": "None",
            "description": "No authentication required for local development"
        },
        "examples": {
            "curl_health": f"curl -X GET http://{config_host}:{settings.PORT}/health",
            "curl_chat": f"curl -X POST http://{config_host}:{settings.PORT}/api/chat/message -H 'Content-Type: application/json' -d '{{\"message\": \"Hello\"}}'",
            "python_client": {
                "description": "Python client example",
                "code": f"""
import requests

# Health check
response = requests.get('http://{config_host}:{settings.PORT}/health')
print(response.json())

# Send chat message
chat_data = {{"message": "Hello, how are you?"}}
response = requests.post('http://{config_host}:{settings.PORT}/api/chat/message', json=chat_data)
print(response.json())
"""
            }
        }
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    local_ip = get_local_ip()
    return {
        "message": "Rilance Code Studio API Server",
        "version": "1.0.0",
        "status": "running",
        "server_ip": local_ip,
        "endpoints": {
            "documentation": f"http://{local_ip}:{settings.PORT}/static/index.html",
            "health": f"http://{local_ip}:{settings.PORT}/health",
            "api_docs": f"http://{local_ip}:{settings.PORT}/api/docs",
            "websocket": f"ws://{local_ip}:{settings.PORT}/ws/{{client_id}}"
        }
    }


@app.get("/health")
async def health_check():
    """Simple health check endpoint"""
    try:
        # Basic health check - just return True if server is running
        return True
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return False

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with comprehensive server information"""
    local_ip = get_local_ip()
    all_ips = get_all_ips()

    # Get available models information
    available_models = []
    try:
        if settings.OPENAI_API_KEY:
            available_models.append({
                "name": "OpenAI GPT",
                "type": "api",
                "model": settings.OPENAI_MODEL,
                "status": "available"
            })
        if settings.MODEL_NAME or settings.MODEL_PATH:
            available_models.append({
                "name": "Local Model",
                "type": "local",
                "model": settings.MODEL_NAME,
                "status": "available"
            })
    except Exception:
        pass

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "server_info": {
            "primary_ip": local_ip,
            "all_addresses": all_ips,
            "port": settings.PORT,
            "host": settings.HOST
        },
        "services": {
            "ai_service": await ai_service.health_check(),
            "websocket_connections": len(websocket_manager.active_connections)
        },
        "models": {
            "available": available_models,
            "count": len(available_models)
        },
        "endpoints": {
            "documentation": f"http://{local_ip}:{settings.PORT}/static/index.html",
            "api_docs": f"http://{local_ip}:{settings.PORT}/api/docs",
            "redoc": f"http://{local_ip}:{settings.PORT}/api/redoc",
            "websocket": f"ws://{local_ip}:{settings.PORT}/ws/{{client_id}}"
        },
        "features": {
            "chat_mode": True,
            "agent_mode": True,
            "file_operations": True,
            "web_search": bool(settings.SEARCH_API_KEY),
            "code_completion": True
        }
    }


@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time communication"""
    # Get client IP for logging
    client_ip = "unknown"
    if websocket.client:
        client_ip = websocket.client.host

    logger.info(f"WEBSOCKET | {client_ip} | Client {client_id} attempting to connect")

    await websocket_manager.connect(websocket, client_id)
    logger.info(f"WEBSOCKET | {client_ip} | Client {client_id} connected successfully")

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            
            # Process message based on type
            message_type = data.get("type")
            
            if message_type == "chat":
                # Handle chat message
                logger.info(f"WEBSOCKET | {client_ip} | Client {client_id} sent chat message")
                response = await ai_service.process_chat_message(
                    message=data.get("message"),
                    context=data.get("context"),
                    conversation_id=data.get("conversation_id")
                )
                await websocket_manager.send_personal_message(response, client_id)
                
            elif message_type == "agent":
                # Handle agent task
                logger.info(f"WEBSOCKET | {client_ip} | Client {client_id} started agent task")
                async for update in ai_service.process_agent_task(
                    task=data.get("task"),
                    context=data.get("context"),
                    project_path=data.get("project_path")
                ):
                    await websocket_manager.send_personal_message(update, client_id)
                    
            elif message_type == "completion":
                # Handle code completion
                response = await ai_service.get_code_completion(
                    code=data.get("code"),
                    cursor_position=data.get("cursor_position"),
                    file_path=data.get("file_path")
                )
                await websocket_manager.send_personal_message(response, client_id)
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(client_id)
        logger.info(f"WEBSOCKET | {client_ip} | Client {client_id} disconnected")
    except Exception as e:
        logger.error(f"WEBSOCKET | {client_ip} | WebSocket error for client {client_id}: {e}")
        websocket_manager.disconnect(client_id)


# Integrated CLI functionality
class IntegratedCLI:
    """Integrated CLI that runs alongside the server"""

    def __init__(self):
        self.cli = None
        self.cli_task = None
        self.running = False
        self.input_thread = None

    async def initialize(self):
        """Initialize the CLI"""
        try:
            from cli import RCSCLI
            self.cli = RCSCLI()
            await self.cli.initialize()
            logger.info("🎮 CLI interface initialized")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize CLI: {e}")
            return False

    def start_input_thread(self):
        """Start input thread for CLI commands"""
        import threading

        def input_loop():
            """Input loop running in separate thread"""
            logger.info("🚀 CLI interface ready - type commands below:")
            logger.info("💡 Available commands: help, models, config, test, health, system, exit")

            while self.running:
                try:
                    # Get input from user
                    user_input = input("\n🚀 RCS> ").strip()

                    if not user_input:
                        continue

                    # Process command synchronously in thread
                    self.process_command_sync(user_input)

                except KeyboardInterrupt:
                    logger.info("👋 CLI interface stopped (server continues running)")
                    self.running = False
                    break
                except EOFError:
                    logger.info("👋 CLI interface stopped (server continues running)")
                    self.running = False
                    break
                except Exception as e:
                    logger.error(f"❌ CLI input error: {e}")

        self.running = True
        self.input_thread = threading.Thread(target=input_loop, daemon=True)
        self.input_thread.start()

    def process_command_sync(self, command_str: str):
        """Process a CLI command synchronously"""
        try:
            parts = command_str.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []

            # Handle exit commands
            if command in ['exit', 'quit', 'stop']:
                logger.info("👋 Stopping CLI interface (server continues running)")
                self.running = False
                return

            # Process command through CLI
            if self.cli and command in self.cli.commands:
                cmd_func = self.cli.commands[command]
                try:
                    # Only call sync functions in thread
                    if not asyncio.iscoroutinefunction(cmd_func):
                        cmd_func(args)
                    else:
                        logger.warning(f"⚠️ Command '{command}' requires async execution, use API endpoint instead")
                except Exception as e:
                    logger.error(f"❌ Command error: {e}")
            else:
                logger.warning(f"❌ Unknown command: '{command}'. Type 'help' for available commands.")

        except Exception as e:
            logger.error(f"❌ Failed to process command: {e}")

    async def process_command(self, command_str: str):
        """Process a CLI command"""
        try:
            parts = command_str.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []

            # Handle exit commands
            if command in ['exit', 'quit', 'stop']:
                logger.info("👋 Stopping CLI interface (server continues running)")
                self.running = False
                return

            # Process command through CLI
            if self.cli and command in self.cli.commands:
                cmd_func = self.cli.commands[command]
                try:
                    if asyncio.iscoroutinefunction(cmd_func):
                        await cmd_func(args)
                    else:
                        cmd_func(args)
                except Exception as e:
                    logger.error(f"❌ Command error: {e}")
            else:
                logger.warning(f"❌ Unknown command: '{command}'. Type 'help' for available commands.")

        except Exception as e:
            logger.error(f"❌ Failed to process command: {e}")

# Global CLI instance
integrated_cli = IntegratedCLI()

# Add CLI endpoint to the FastAPI app (for API access)
@app.post("/api/cli")
async def execute_cli_command(request: dict):
    """Execute CLI command via API"""
    try:
        command_str = request.get("command", "").strip()
        if not command_str:
            return {"error": "No command provided"}

        # Use the integrated CLI instance
        if not integrated_cli.cli:
            await integrated_cli.initialize()

        parts = command_str.split()
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []

        # Execute command
        if command in integrated_cli.cli.commands:
            cmd_func = integrated_cli.cli.commands[command]

            # Capture output
            import io
            import contextlib

            output_buffer = io.StringIO()

            try:
                with contextlib.redirect_stdout(output_buffer):
                    if asyncio.iscoroutinefunction(cmd_func):
                        await cmd_func(args)
                    else:
                        cmd_func(args)

                output = output_buffer.getvalue()
                return {
                    "success": True,
                    "command": command_str,
                    "output": output,
                    "message": f"Command '{command}' executed successfully"
                }

            except Exception as e:
                return {
                    "success": False,
                    "command": command_str,
                    "error": str(e),
                    "message": f"Command '{command}' failed"
                }
        else:
            return {
                "success": False,
                "command": command_str,
                "error": f"Unknown command: {command}",
                "message": "Use 'help' to see available commands"
            }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to execute command"
        }


@app.get("/api/cli/help")
async def get_cli_help():
    """Get CLI help information"""
    try:
        if not integrated_cli.cli:
            await integrated_cli.initialize()

        # Capture help output
        import io
        import contextlib

        output_buffer = io.StringIO()

        with contextlib.redirect_stdout(output_buffer):
            integrated_cli.cli.show_help()

        help_text = output_buffer.getvalue()

        return {
            "success": True,
            "help": help_text,
            "available_commands": list(integrated_cli.cli.commands.keys())
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get help information"
        }


def main():
    """Main entry point - unified server with integrated CLI"""
    # Load configuration first
    config = load_config()
    apply_config_to_settings(config)

    parser = argparse.ArgumentParser(description="Reverie Code Studio Server")
    parser.add_argument(
        "--host",
        default=config["server"]["host"],
        help=f"Server host - use 0.0.0.0 for all interfaces (default: {config['server']['host']})"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=config["server"]["port"],
        help=f"Server port (default: {config['server']['port']})"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        default=config["server"]["debug"],
        help="Enable debug mode"
    )
    parser.add_argument(
        "--config",
        action="store_true",
        help="Show current configuration and exit"
    )


    args = parser.parse_args()

    # Show config if requested
    if args.config:
        print("📄 Current Configuration:")
        print("=" * 50)
        print(json.dumps(config, indent=2, ensure_ascii=False))
        return

    # Configure logging with enhanced colors and formatting
    logger.remove()

    # Enhanced logging for unified server mode
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="DEBUG" if args.debug else "INFO",
        colorize=True
    )

    # Add file logging
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    logger.add(
        log_dir / "rcs_server.log",
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG" if args.debug else "INFO"
    )

    # Start unified server with integrated CLI
    logger.info("Starting Rilance Code Studio Server with Integrated CLI...")

    # Display server information using config values
    config_host = args.host

    # Get actual deployment IP based on config
    if config_host == "0.0.0.0":
        # If binding to all interfaces, show actual network IPs
        local_ip = get_local_ip()
        all_ips = get_all_ips()
        client_ips = get_client_connection_ips()
    else:
        # If binding to specific IP, use that IP
        local_ip = config_host
        all_ips = [f"IP: {config_host}"]
        client_ips = [config_host]

    logger.info("=" * 80)
    logger.info("🚀 RILANCE CODE STUDIO UNIFIED SERVER")
    logger.info("=" * 80)
    logger.info(f"📍 Server Host: {args.host}")
    logger.info(f"🔌 Server Port: {args.port}")
    logger.info(f"🌐 Primary IP: {local_ip}")
    logger.info(f"🎮 CLI Interface: ✅ Integrated via API")
    logger.info("")

    # Client connection information - MOST IMPORTANT
    logger.info("🎯 CLIENT CONNECTION INFORMATION:")
    logger.info("   ┌─────────────────────────────────────────────────────────┐")
    logger.info("   │  📱 RCS IDE Client Connection Settings:                │")
    logger.info("   │                                                         │")
    for i, ip in enumerate(client_ips, 1):
        primary_marker = " (PRIMARY)" if ip == local_ip else " (LOCALHOST)" if ip == "127.0.0.1" else " (CONFIG)"
        logger.info(f"   │  {i}. Server IP: {ip}:{args.port}{primary_marker:<15} │")
    logger.info("   │                                                         │")
    logger.info("   │  💡 Use any of the above IP addresses in your client   │")
    logger.info("   │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │")
    logger.info("   │  ⚙️  Configuration: config.json (host: {config_host})    │")
    logger.info("   └─────────────────────────────────────────────────────────┘")
    logger.info("")

    logger.info("📡 All Available Network Addresses:")
    for ip_info in all_ips:
        logger.info(f"   • {ip_info}")
    logger.info("")
    logger.info("📋 Web Interface URLs:")
    logger.info(f"   🏠 Main Page: http://{local_ip}:{args.port}/")
    logger.info(f"   📖 Documentation: http://{local_ip}:{args.port}/static/index.html")
    logger.info(f"   ❤️  Health Check: http://{local_ip}:{args.port}/health")
    logger.info(f"   📚 API Docs: http://{local_ip}:{args.port}/api/docs")
    logger.info("")

    logger.info("🔌 API Endpoints for Client:")
    logger.info(f"   📡 HTTP API: http://{local_ip}:{args.port}/api/")
    logger.info(f"   🌐 WebSocket: ws://{local_ip}:{args.port}/ws/{{client_id}}")
    logger.info(f"   🔍 Health Check: http://{local_ip}:{args.port}/health")
    logger.info("")

    # Display available models with enhanced detection
    logger.info("🤖 AI Models Status:")
    try:
        # Initialize model manager to get accurate model info
        from app.services.model_manager import ModelManager
        temp_model_manager = ModelManager()

        # Run async initialization in sync context
        async def get_models_info():
            await temp_model_manager.initialize()
            return await temp_model_manager.get_available_models()

        models = asyncio.run(get_models_info())

        # Local models
        local_models = models.get("local", [])
        if local_models:
            logger.info(f"   📦 Local Models ({len(local_models)} found):")
            for model in local_models:
                status_icon = "✅" if model["status"] == "available" else "⚠️"
                logger.info(f"      {status_icon} {model['name']} ({model.get('size', 'Unknown size')})")
        else:
            logger.info("   📦 Local Models: None found in ./data/models")

        # API models
        if settings.OPENAI_API_KEY:
            logger.info(f"   🌐 OpenAI Models: ✅ Available ({settings.OPENAI_MODEL})")
        else:
            logger.info("   🌐 OpenAI Models: ❌ No API key configured")

        # HuggingFace models
        hf_models = models.get("huggingface", [])
        logger.info(f"   🤗 HuggingFace Models: {len(hf_models)} predefined models available")

    except Exception as e:
        logger.warning(f"⚠️ Could not load model information: {e}")

    # Enhanced configuration info
    logger.info("")
    logger.info("⚙️ Server Configuration:")
    logger.info(f"   🏠 Host: {args.host}")
    logger.info(f"   🔌 Port: {args.port}")
    logger.info(f"   🐛 Debug Mode: {'✅ Enabled' if args.debug else '❌ Disabled'}")
    logger.info(f"   📝 Log Level: {'DEBUG' if args.debug else 'INFO'}")
    logger.info(f"   📁 Log Directory: {Path('logs').absolute()}")

    # CLI Integration info
    logger.info("")
    logger.info("🎮 Integrated CLI Commands (via API):")
    logger.info("   • POST /api/cli                 - Execute CLI commands")
    logger.info("   • GET /api/cli/help             - Get CLI help")
    logger.info("   • Available commands: help, models, config, test, health, system")
    logger.info("   • Example: POST /api/cli {'command': 'models list'}")
    logger.info("   • Example: POST /api/cli {'command': 'test chat Hello'}")

    # Quick start guide
    logger.info("")
    logger.info("🎯 Quick Start Guide:")
    logger.info("   1. 🖥️ Start RCS IDE Client Application")
    logger.info("   2. 🔗 Enter server IP in client (use one from above)")
    logger.info("   3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}")
    logger.info("   4. 💻 Begin coding with AI assistance!")
    logger.info("   5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html")

    logger.info("=" * 80)
    logger.info("🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.")
    logger.info("=" * 80)

    # Start the FastAPI server with integrated CLI
    try:
        uvicorn.run(
            app,  # Use app directly instead of string
            host=args.host,
            port=args.port,
            reload=False,  # Disable reload for CLI integration
            log_level="info",
            access_log=False  # We handle access logging ourselves
        )
    except KeyboardInterrupt:
        logger.info("👋 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
