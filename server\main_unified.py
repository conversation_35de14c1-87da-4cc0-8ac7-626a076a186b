#!/usr/bin/env python3
"""
Rilance Code Studio (RCS) - Unified Server with Integrated CLI
A unified server that combines web API and CLI functionality in one process.
"""

import argparse
import asyncio
import json
import os
import sys
import threading
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from loguru import logger

# Configuration management
from app.core.config import settings
import json
from typing import Dict, Any
import socket

# Configuration functions
def get_default_config() -> Dict[str, Any]:
    """Get default configuration"""
    return {
        "server": {
            "host": "127.0.0.1",
            "port": 27027,
            "debug": True,
            "log_level": "INFO"
        },
        "ai": {
            "default_model": "Qwen/Qwen1.5-4B",
            "model_path": "",
            "device": "auto",
            "max_length": 32768,
            "temperature": 0.7,
            "openai_api_key": "",
            "openai_model": ""
        },
        "features": {
            "web_search": False,
            "search_api_key": "",
            "file_operations": True,
            "code_completion": True,
            "agent_mode": True,
            "chat_mode": True
        },
        "security": {
            "cors_origins": ["*"],
            "max_file_size": 10 * 1024 * 1024,
            "allowed_extensions": [".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", ".json", ".md", ".txt"]
        },
        "storage": {
            "data_dir": "./data",
            "models_dir": "./data/models",
            "conversations_dir": "./data/conversations",
            "uploads_dir": "./data/uploads"
        }
    }

def load_config() -> Dict[str, Any]:
    """Load configuration from config.json or create default"""
    from pathlib import Path
    config_file = Path("config.json")

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info("📄 Loaded configuration from config.json")
            return config
        except Exception as e:
            logger.warning(f"⚠️ Failed to load config.json: {e}")
            logger.info("📄 Using default configuration")

    # Create default config
    config = get_default_config()
    save_config(config)
    return config

def save_config(config: Dict[str, Any]):
    """Save configuration to config.json"""
    try:
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        logger.info("💾 Configuration saved to config.json")
    except Exception as e:
        logger.error(f"❌ Failed to save config.json: {e}")

def apply_config_to_settings(config: Dict[str, Any]):
    """Apply configuration to settings"""
    try:
        # Server settings
        server_config = config.get("server", {})
        settings.HOST = server_config.get("host", settings.HOST)
        settings.PORT = server_config.get("port", settings.PORT)
        settings.DEBUG = server_config.get("debug", settings.DEBUG)

        # AI settings
        ai_config = config.get("ai", {})
        settings.MODEL_NAME = ai_config.get("default_model", settings.MODEL_NAME)
        settings.MODEL_PATH = ai_config.get("model_path", settings.MODEL_PATH)
        settings.DEVICE = ai_config.get("device", settings.DEVICE)
        settings.MAX_LENGTH = ai_config.get("max_length", settings.MAX_LENGTH)
        settings.MAX_NEW_TOKENS = ai_config.get("max_new_tokens", settings.MAX_NEW_TOKENS)
        settings.CONTEXT_LENGTH = ai_config.get("context_length", settings.CONTEXT_LENGTH)
        settings.TEMPERATURE = ai_config.get("temperature", settings.TEMPERATURE)
        settings.TOP_P = ai_config.get("top_p", settings.TOP_P)
        settings.TOP_K = ai_config.get("top_k", settings.TOP_K)
        settings.REPETITION_PENALTY = ai_config.get("repetition_penalty", settings.REPETITION_PENALTY)
        settings.DO_SAMPLE = ai_config.get("do_sample", settings.DO_SAMPLE)
        settings.OPENAI_API_KEY = ai_config.get("openai_api_key", settings.OPENAI_API_KEY)
        settings.OPENAI_MODEL = ai_config.get("openai_model", settings.OPENAI_MODEL)
        settings.OPENAI_MAX_TOKENS = ai_config.get("openai_max_tokens", settings.OPENAI_MAX_TOKENS)

        logger.info("⚙️ Configuration applied to settings")
    except Exception as e:
        logger.error(f"❌ Failed to apply configuration: {e}")

def get_local_ip() -> str:
    """Get local IP address"""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

# Integrated CLI functionality
class IntegratedCLI:
    """Integrated CLI that runs alongside the server"""
    
    def __init__(self):
        self.cli = None
        self.input_thread = None
        self.running = False
        
    async def initialize(self):
        """Initialize the CLI"""
        try:
            from cli import RCSCLI
            self.cli = RCSCLI()
            await self.cli.initialize()
            logger.info("🎮 CLI interface initialized")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize CLI: {e}")
            return False
    
    def start_input_thread(self):
        """Start input thread for CLI commands"""
        import threading

        def input_loop():
            """Input loop running in separate thread"""
            logger.info("🚀 CLI interface ready - type commands below:")
            logger.info("💡 Available commands: help, models, config, test, health, system, exit")

            while self.running:
                try:
                    # Get input from user
                    user_input = input("\n🚀 RCS> ").strip()

                    if not user_input:
                        continue

                    # Process command synchronously in thread
                    self.process_command_sync(user_input)

                except KeyboardInterrupt:
                    logger.info("👋 CLI interface stopped (server continues running)")
                    self.running = False
                    break
                except EOFError:
                    logger.info("👋 CLI interface stopped (server continues running)")
                    self.running = False
                    break
                except Exception as e:
                    logger.error(f"❌ CLI input error: {e}")

        self.running = True
        self.input_thread = threading.Thread(target=input_loop, daemon=True)
        self.input_thread.start()

    def process_command_sync(self, command_str: str):
        """Process a CLI command synchronously"""
        try:
            parts = command_str.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []

            # Handle exit commands
            if command in ['exit', 'quit']:
                logger.info("👋 Stopping CLI interface (server continues running)")
                self.running = False
                return
            elif command == 'stop':
                logger.info("🛑 Stopping entire server...")
                self.running = False
                # Stop the entire server
                import signal
                import os
                os.kill(os.getpid(), signal.SIGTERM)
                return

            # Process command through CLI
            if self.cli and command in self.cli.commands:
                cmd_func = self.cli.commands[command]
                try:
                    if not asyncio.iscoroutinefunction(cmd_func):
                        # Execute synchronous command directly
                        cmd_func(args)
                    else:
                        # Execute async command using asyncio.run()
                        try:
                            # Try to run in new event loop
                            asyncio.run(cmd_func(args))
                        except RuntimeError as e:
                            if "cannot be called from a running event loop" in str(e):
                                # If we're in a running event loop, use run_coroutine_threadsafe
                                try:
                                    import concurrent.futures
                                    import threading

                                    # Get the main event loop
                                    main_loop = None
                                    for thread in threading.enumerate():
                                        if hasattr(thread, '_target') and thread._target and hasattr(thread._target, '__name__'):
                                            continue
                                        # Try to find the main thread's event loop
                                        try:
                                            if hasattr(asyncio, '_get_running_loop'):
                                                main_loop = asyncio._get_running_loop()
                                                break
                                        except:
                                            continue

                                    if main_loop:
                                        # Submit to main event loop and wait for result
                                        future = asyncio.run_coroutine_threadsafe(cmd_func(args), main_loop)
                                        future.result(timeout=30)  # 30 second timeout
                                    else:
                                        # Fallback: create new thread with new event loop
                                        def run_async_command():
                                            asyncio.run(cmd_func(args))

                                        thread = threading.Thread(target=run_async_command)
                                        thread.start()
                                        thread.join(timeout=30)

                                except Exception as inner_e:
                                    logger.error(f"❌ Failed to execute async command: {inner_e}")
                            else:
                                raise e

                except Exception as e:
                    logger.error(f"❌ Command error: {e}")
                    if settings.DEBUG:
                        import traceback
                        traceback.print_exc()
            else:
                logger.warning(f"❌ Unknown command: '{command}'. Type 'help' for available commands.")

        except Exception as e:
            logger.error(f"❌ Failed to process command: {e}")
            if settings.DEBUG:
                import traceback
                traceback.print_exc()
    
    async def process_command(self, command_str: str):
        """Process a CLI command"""
        try:
            parts = command_str.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []
            
            # Handle exit commands
            if command in ['exit', 'quit']:
                logger.info("👋 Stopping CLI interface (server continues running)")
                self.running = False
                return
            elif command == 'stop':
                logger.info("🛑 Stopping entire server...")
                self.running = False
                # Stop the entire server
                import signal
                import os
                os.kill(os.getpid(), signal.SIGTERM)
                return
            
            # Process command through CLI
            if self.cli and command in self.cli.commands:
                cmd_func = self.cli.commands[command]
                try:
                    if asyncio.iscoroutinefunction(cmd_func):
                        await cmd_func(args)
                    else:
                        cmd_func(args)
                except Exception as e:
                    logger.error(f"❌ Command error: {e}")
            else:
                logger.warning(f"❌ Unknown command: '{command}'. Type 'help' for available commands.")
                
        except Exception as e:
            logger.error(f"❌ Failed to process command: {e}")

# Global CLI instance
integrated_cli = IntegratedCLI()

# FastAPI app setup
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Reverie Code Studio Server...")

    # Create necessary directories
    os.makedirs("data/conversations", exist_ok=True)
    os.makedirs("data/projects", exist_ok=True)
    os.makedirs("data/temp", exist_ok=True)
    os.makedirs("data/uploads", exist_ok=True)
    os.makedirs("static", exist_ok=True)

    # Initialize and start integrated CLI
    try:
        await integrated_cli.initialize()
        # Start CLI input thread
        integrated_cli.start_input_thread()
        logger.info("Integrated CLI started")
    except Exception as e:
        logger.warning(f"Failed to start CLI: {e}")

    logger.info("Server startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Reverie Code Studio Server...")
    try:
        # Stop CLI
        integrated_cli.running = False
        if integrated_cli.input_thread and integrated_cli.input_thread.is_alive():
            integrated_cli.input_thread.join(timeout=1.0)
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

# Create FastAPI app
app = FastAPI(
    title="Rilance Code Studio API",
    description="Unified AI-powered development environment with integrated CLI",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
try:
    from app.api import models
    app.include_router(models.router, prefix="/api/models", tags=["models"])
    logger.info("✅ Models API router included")
except Exception as e:
    logger.warning(f"⚠️ Failed to include models router: {e}")

try:
    from app.api import agent
    app.include_router(agent.router, prefix="/api/agent", tags=["agent"])
    logger.info("✅ Agent API router included")
except Exception as e:
    logger.warning(f"⚠️ Failed to include agent router: {e}")

try:
    from app.api import chat
    app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
    logger.info("✅ Chat API router included")
except Exception as e:
    logger.warning(f"⚠️ Failed to include chat router: {e}")

try:
    from app.api import files
    app.include_router(files.router, prefix="/api/files", tags=["files"])
    logger.info("✅ Files API router included")
except Exception as e:
    logger.warning(f"⚠️ Failed to include files router: {e}")

# Static files (check if directory exists)
import os
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")
else:
    logger.warning("Static directory not found, skipping static file mounting")

# Basic health endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "RCS Unified Server",
        "cli_integrated": True,
        "timestamp": asyncio.get_event_loop().time()
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint - serve the main dashboard"""
    try:
        # Try to serve the static HTML file
        static_file_path = os.path.join("static", "index.html")
        if os.path.exists(static_file_path):
            with open(static_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return HTMLResponse(content)
        else:
            # Fallback to basic HTML if static file doesn't exist
            return HTMLResponse("""
            <html>
                <head>
                    <title>Rilance Code Studio</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        h1 { color: #333; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
                        .status { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
                        .link-card { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; text-align: center; }
                        .link-card a { text-decoration: none; color: #007bff; font-weight: bold; }
                        .link-card:hover { background: #e9ecef; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🚀 Rilance Code Studio - Unified Server</h1>
                        <div class="status">
                            <strong>✅ Server Status:</strong> Running with integrated CLI functionality!
                        </div>
                        <p>Welcome to the Rilance Code Studio server. The server is running and ready to accept connections.</p>
                        <div class="links">
                            <div class="link-card">
                                <a href="/health">🔧 Health Check</a>
                                <p>Check server health and status</p>
                            </div>
                            <div class="link-card">
                                <a href="/api/cli/help">💻 CLI Help</a>
                                <p>Get CLI commands and help</p>
                            </div>
                            <div class="link-card">
                                <a href="/docs">📚 API Documentation</a>
                                <p>Interactive API documentation</p>
                            </div>
                            <div class="link-card">
                                <a href="/static/index.html">📋 Full Dashboard</a>
                                <p>Complete server dashboard</p>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
            """)
    except Exception as e:
        logger.error(f"Error serving root page: {e}")
        return HTMLResponse(f"""
        <html>
            <head><title>Rilance Code Studio - Error</title></head>
            <body>
                <h1>🚀 Rilance Code Studio</h1>
                <p>Server is running, but there was an error loading the dashboard.</p>
                <p>Error: {str(e)}</p>
                <ul>
                    <li><a href="/health">Health Check</a></li>
                    <li><a href="/docs">API Documentation</a></li>
                </ul>
            </body>
        </html>
        """, status_code=500)

# CLI API endpoints
@app.post("/api/cli")
async def execute_cli_command(request: dict):
    """Execute CLI command via API"""
    try:
        command_str = request.get("command", "").strip()
        if not command_str:
            return {"error": "No command provided"}
        
        # Use the integrated CLI instance
        if not integrated_cli.cli:
            await integrated_cli.initialize()
        
        parts = command_str.split()
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        # Execute command
        if command in integrated_cli.cli.commands:
            cmd_func = integrated_cli.cli.commands[command]
            
            # Capture output
            import io
            import contextlib
            
            output_buffer = io.StringIO()
            
            try:
                with contextlib.redirect_stdout(output_buffer):
                    if asyncio.iscoroutinefunction(cmd_func):
                        await cmd_func(args)
                    else:
                        cmd_func(args)
                
                output = output_buffer.getvalue()
                return {
                    "success": True,
                    "command": command_str,
                    "output": output,
                    "message": f"Command '{command}' executed successfully"
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "command": command_str,
                    "error": str(e),
                    "message": f"Command '{command}' failed"
                }
        else:
            return {
                "success": False,
                "command": command_str,
                "error": f"Unknown command: {command}",
                "message": "Use 'help' to see available commands"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to execute command"
        }

@app.get("/api/cli/help")
async def get_cli_help():
    """Get CLI help information"""
    try:
        if not integrated_cli.cli:
            await integrated_cli.initialize()

        # Capture help output
        import io
        import contextlib

        output_buffer = io.StringIO()

        with contextlib.redirect_stdout(output_buffer):
            integrated_cli.cli.show_help()

        help_text = output_buffer.getvalue()

        return {
            "success": True,
            "help": help_text,
            "available_commands": list(integrated_cli.cli.commands.keys())
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get help information"
        }

# API endpoint for models
@app.get("/api/models/available")
async def get_available_models():
    """Get list of available AI models with detailed information"""
    try:
        from app.services.model_manager import ModelManager

        # Initialize model manager to get accurate model info
        model_manager = ModelManager()
        await model_manager.initialize()

        all_models = await model_manager.get_available_models()
        formatted_models = []

        # Format models for the web interface
        for model in all_models:
            # Handle both dict and string model entries
            if isinstance(model, dict):
                formatted_model = {
                    "name": model.get("name", "Unknown"),
                    "type": model.get("type", "unknown"),
                    "status": model.get("status", "unknown"),
                    "description": model.get("description", "No description available"),
                    "size": model.get("size", "Unknown"),
                    "capabilities": model.get("capabilities", [])
                }
            else:
                # Handle string entries
                formatted_model = {
                    "name": str(model),
                    "type": "unknown",
                    "status": "unknown",
                    "description": "No description available",
                    "size": "Unknown",
                    "capabilities": []
                }
            formatted_models.append(formatted_model)

        return {
            "success": True,
            "models": formatted_models,
            "count": len(formatted_models)
        }

    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get available models"
        }

# API information endpoint
@app.get("/api")
async def api_info():
    """API information endpoint"""
    local_ip = get_local_ip()
    return {
        "message": "Rilance Code Studio API Server",
        "version": "1.0.0",
        "status": "running",
        "server_ip": local_ip,
        "endpoints": {
            "documentation": f"http://{local_ip}:{settings.PORT}/static/index.html",
            "health": f"http://{local_ip}:{settings.PORT}/health",
            "api_docs": f"http://{local_ip}:{settings.PORT}/docs",
            "websocket": f"ws://{local_ip}:{settings.PORT}/ws/{{client_id}}"
        }
    }

# API endpoint for models
@app.get("/api/models/available")
async def get_available_models():
    """Get list of available AI models with detailed information"""
    try:
        from app.services.model_manager import ModelManager

        # Initialize model manager to get accurate model info
        model_manager = ModelManager()
        await model_manager.initialize()

        all_models = await model_manager.get_available_models()
        formatted_models = []

        # Format models for the web interface
        for model in all_models:
            formatted_model = {
                "name": model.get("name", "Unknown"),
                "type": model.get("type", "unknown"),
                "status": model.get("status", "unknown"),
                "description": model.get("description", "No description available"),
                "size": model.get("size", "Unknown"),
                "capabilities": model.get("capabilities", [])
            }
            formatted_models.append(formatted_model)

        # Separate by type
        local_models = [m for m in formatted_models if m["type"] == "local"]
        hf_models = [m for m in formatted_models if m["type"] == "huggingface"]
        openai_models = [m for m in formatted_models if m["type"] == "openai"]

        return {
            "success": True,
            "models": formatted_models,
            "count": len(formatted_models),
            "categories": {
                "local": len(local_models),
                "huggingface": len(hf_models),
                "openai": len([m for m in openai_models if m.get("status") == "available"])
            },
            "default_model": settings.OPENAI_MODEL if settings.OPENAI_API_KEY else (settings.MODEL_NAME or "none"),
            "features": {
                "streaming": True,
                "function_calling": bool(settings.OPENAI_API_KEY),
                "code_completion": True,
                "web_search": bool(getattr(settings, 'SEARCH_API_KEY', None)),
                "file_operations": True,
                "local_models": len(local_models) > 0
            }
        }

    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get available models"
        }

# API information endpoint
@app.get("/api")
async def api_info():
    """API information endpoint"""
    local_ip = get_local_ip()
    return {
        "message": "Rilance Code Studio API Server",
        "version": "1.0.0",
        "status": "running",
        "server_ip": local_ip,
        "endpoints": {
            "documentation": f"http://{local_ip}:{settings.PORT}/static/index.html",
            "health": f"http://{local_ip}:{settings.PORT}/health",
            "api_docs": f"http://{local_ip}:{settings.PORT}/docs",
            "websocket": f"ws://{local_ip}:{settings.PORT}/ws/{{client_id}}"
        }
    }

def main():
    """Main entry point - Unified Server with integrated CLI"""
    try:
        # Load configuration
        config = load_config()
        apply_config_to_settings(config)

        # Parse command line arguments
        parser = argparse.ArgumentParser(description="Reverie Code Studio Server")
        parser.add_argument(
            "--host",
            default=config["server"]["host"],
            help=f"Server host - use 0.0.0.0 for all interfaces (default: {config['server']['host']})"
        )
        parser.add_argument(
            "--port",
            type=int,
            default=config["server"]["port"],
            help=f"Server port (default: {config['server']['port']})"
        )
        parser.add_argument(
            "--debug",
            action="store_true",
            default=config["server"]["debug"],
            help="Enable debug mode"
        )
        parser.add_argument(
            "--config",
            action="store_true",
            help="Show current configuration and exit"
        )

        args = parser.parse_args()

        # Show config if requested
        if args.config:
            print("📄 Current Configuration:")
            print("=" * 50)
            print(json.dumps(config, indent=2, ensure_ascii=False))
            return

        # Configure logging
        logger.remove()
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="DEBUG" if args.debug else "INFO",
            colorize=True
        )

        # Start unified server with integrated CLI
        logger.info(f"🚀 Starting RCS Server with Integrated CLI on {args.host}:{args.port}")
        logger.info(f"📊 Web Interface: http://{args.host}:{args.port}")
        logger.info(f"📚 API Documentation: http://{args.host}:{args.port}/docs")
        logger.info(f"🔧 Server Configuration: {config}")
        logger.info(f"🎮 CLI commands available after server starts")

        # Start the server (CLI will be started in lifespan)
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            reload=False,  # Disable reload for CLI integration
            log_level="info",
            access_log=False
        )
            
    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
