"""
AI Interaction Sidebar for RCS IDE - Cursor Style
"""

import os
import re
import requests
from datetime import datetime
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QLabel, QComboBox, QScrollArea, QFrame, QTextBrowser,
    QStackedWidget, QButtonGroup, QMenu, QProgressBar,
    QListWidget, QListWidgetItem, QLineEdit, QPlainTextEdit,
    QDialog, QVBoxLayout, QHBoxLayout, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QThread
from PyQt6.QtGui import QFont, QSyntaxHighlighter, QTextCharFormat, QAction, QTextCursor
from loguru import logger

from api.client import APIClient, ChatMessage, AgentTask
from utils.config import config
from .copilot_styles import (
    get_sidebar_style, get_header_style,
    get_connection_widget_style, get_button_style,
    get_model_selector_style, get_chat_bubble_style,
    get_input_area_style, get_scrollbar_style
)


class StreamingTextWidget(QTextBrowser):
    """Text widget with streaming/typewriter effect for AI responses"""

    streaming_finished = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.streaming_timer = QTimer()
        self.streaming_timer.timeout.connect(self._stream_next_char)
        self.streaming_text = ""
        self.current_pos = 0
        self.streaming_speed = 20  # milliseconds between characters
        self.is_streaming = False

        # Configure for better text display
        self.setOpenExternalLinks(True)
        self.setWordWrapMode(True)

    def start_streaming_text(self, text: str, speed: int = 20):
        """Start streaming text with typewriter effect"""
        self.streaming_text = text
        self.current_pos = 0
        self.streaming_speed = speed
        self.is_streaming = True

        # Clear current content
        self.clear()

        # Start streaming
        self.streaming_timer.start(self.streaming_speed)

    def _stream_next_char(self):
        """Add next character to display"""
        if self.current_pos >= len(self.streaming_text):
            self.streaming_timer.stop()
            self.is_streaming = False
            self.streaming_finished.emit()
            return

        # Add next character
        char = self.streaming_text[self.current_pos]
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.insertText(char)

        # Auto-scroll to bottom
        scrollbar = self.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        self.current_pos += 1

    def set_streaming_speed(self, speed: int):
        """Set streaming speed in milliseconds"""
        self.streaming_speed = speed
        if self.streaming_timer.isActive():
            self.streaming_timer.setInterval(speed)

    def stop_streaming(self):
        """Stop streaming and show full text immediately"""
        if self.is_streaming:
            self.streaming_timer.stop()
            self.is_streaming = False

            # Show remaining text
            remaining_text = self.streaming_text[self.current_pos:]
            if remaining_text:
                cursor = self.textCursor()
                cursor.movePosition(QTextCursor.MoveOperation.End)
                cursor.insertText(remaining_text)

            self.streaming_finished.emit()

    def set_text_instantly(self, text: str):
        """Set text without streaming effect"""
        self.stop_streaming()
        self.setPlainText(text)


class ExpandableTextEdit(QTextEdit):
    """Auto-expanding text edit for long input support"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(40)
        self.setMaximumHeight(300)  # Allow expansion up to 300px

        # Connect to text changes for auto-resize
        self.textChanged.connect(self._adjust_height)

    def _adjust_height(self):
        """Adjust height based on content"""
        # Get document height
        doc_height = self.document().size().height()

        # Calculate needed height (with some padding)
        needed_height = int(doc_height + 20)

        # Constrain to min/max
        needed_height = max(40, min(300, needed_height))

        # Set new height if different
        if needed_height != self.height():
            self.setFixedHeight(needed_height)

    def keyPressEvent(self, event):
        """Handle key press events for better UX"""
        # Ctrl+Enter or Shift+Enter for new line
        if event.key() == Qt.Key.Key_Return:
            if event.modifiers() & (Qt.KeyboardModifier.ControlModifier | Qt.KeyboardModifier.ShiftModifier):
                # Insert new line
                super().keyPressEvent(event)
            else:
                # Emit custom signal for sending message
                self.parent().parent()._send_message()
        else:
            super().keyPressEvent(event)


class MarkdownHighlighter(QSyntaxHighlighter):
    """Syntax highlighter for markdown content"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_formats()

    def _setup_formats(self):
        """Setup text formats for different markdown elements"""
        # Code blocks
        self.code_format = QTextCharFormat()
        self.code_format.setBackground(Qt.GlobalColor.darkGray)
        self.code_format.setForeground(Qt.GlobalColor.lightGray)
        self.code_format.setFontFamily("Consolas, Monaco, monospace")

        # Inline code
        self.inline_code_format = QTextCharFormat()
        self.inline_code_format.setBackground(Qt.GlobalColor.darkGray)
        self.inline_code_format.setForeground(Qt.GlobalColor.cyan)
        self.inline_code_format.setFontFamily("Consolas, Monaco, monospace")

        # Headers
        self.header_format = QTextCharFormat()
        self.header_format.setForeground(Qt.GlobalColor.white)
        self.header_format.setFontWeight(QFont.Weight.Bold)

        # Bold text
        self.bold_format = QTextCharFormat()
        self.bold_format.setFontWeight(QFont.Weight.Bold)

        # Italic text
        self.italic_format = QTextCharFormat()
        self.italic_format.setFontItalic(True)

    def highlightBlock(self, text):
        """Highlight markdown syntax in text block"""
        # Code blocks (```)
        code_pattern = r'```[\s\S]*?```'
        for match in re.finditer(code_pattern, text):
            self.setFormat(match.start(), match.end() - match.start(), self.code_format)

        # Inline code (`)
        inline_code_pattern = r'`[^`]+`'
        for match in re.finditer(inline_code_pattern, text):
            self.setFormat(match.start(), match.end() - match.start(), self.inline_code_format)

        # Headers (#)
        header_pattern = r'^#{1,6}\s+.*$'
        for match in re.finditer(header_pattern, text, re.MULTILINE):
            self.setFormat(match.start(), match.end() - match.start(), self.header_format)

        # Bold (**text**)
        bold_pattern = r'\*\*([^*]+)\*\*'
        for match in re.finditer(bold_pattern, text):
            self.setFormat(match.start(), match.end() - match.start(), self.bold_format)

        # Italic (*text*)
        italic_pattern = r'\*([^*]+)\*'
        for match in re.finditer(italic_pattern, text):
            self.setFormat(match.start(), match.end() - match.start(), self.italic_format)


class ChatBubble(QFrame):
    """Enhanced chat bubble widget with Cursor-style design and streaming support"""

    streaming_finished = pyqtSignal()

    def __init__(self, message: str, is_user: bool = True, timestamp: str = None,
                 message_type: str = "text", context_files: List[str] = None,
                 enable_streaming: bool = False, parent=None):
        super().__init__(parent)
        self.message = message
        self.is_user = is_user
        self.timestamp = timestamp or datetime.now().strftime("%H:%M")
        self.message_type = message_type  # text, code, diff, thinking
        self.context_files = context_files or []
        self.enable_streaming = enable_streaming and not is_user  # Only AI messages stream
        self.content_widget = None

        self._setup_ui()

    def _setup_ui(self):
        """Setup enhanced chat bubble UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(6)

        # Context files display (if any)
        if self.context_files and not self.is_user:
            self._add_context_display(layout)

        # Message content
        if self.message_type == "code" or "```" in self.message:
            self.content_widget = self._create_code_content()
        else:
            self.content_widget = self._create_text_content()

        layout.addWidget(self.content_widget)

        # Timestamp and actions
        self._add_footer(layout)

        # Apply styling based on sender
        self._apply_bubble_style()

    def _create_text_content(self) -> QWidget:
        """Create text content widget with markdown support and streaming"""
        if self.enable_streaming:
            content = StreamingTextWidget()
            content.streaming_finished.connect(self.streaming_finished.emit)
        else:
            content = QTextBrowser()
            content.setOpenExternalLinks(True)

        content.setStyleSheet("""
            QTextBrowser {
                background: transparent;
                border: none;
                color: inherit;
                font-size: 14px;
                line-height: 1.4;
                font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            }
        """)

        # Set content
        if self.enable_streaming:
            # Start streaming after a short delay
            QTimer.singleShot(100, lambda: content.start_streaming_text(self.message, 30))
        else:
            content.setMarkdown(self.message)
            # Set minimum height based on content
            doc = content.document()
            content.setMaximumHeight(int(doc.size().height() + 10))

        return content

    def _create_code_content(self) -> QWidget:
        """Create code content widget with syntax highlighting and streaming"""
        if self.enable_streaming:
            content = StreamingTextWidget()
            content.streaming_finished.connect(self.streaming_finished.emit)
        else:
            content = QTextBrowser()

        content.setStyleSheet(f"""
            QTextBrowser {{
                background-color: #0d1117;
                border: 1px solid #333333;
                border-radius: 8px;
                color: #e6edf3;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                padding: 16px;
                line-height: 1.4;
            }}
        """)

        # Set content
        if self.enable_streaming:
            # Start streaming with slower speed for code
            QTimer.singleShot(100, lambda: content.start_streaming_text(self.message, 50))
        else:
            content.setPlainText(self.message)

        # Add syntax highlighting
        MarkdownHighlighter(content.document())

        return content

    def _add_context_display(self, layout: QVBoxLayout):
        """Add context files display"""
        context_frame = QFrame()
        context_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 102, 204, 0.1);
                border-left: 3px solid #0066cc;
                border-radius: 6px;
                padding: 6px;
                margin: 4px 0px;
            }
        """)

        context_layout = QVBoxLayout(context_frame)
        context_layout.setContentsMargins(8, 4, 8, 4)

        context_label = QLabel("📁 Context:")
        context_label.setStyleSheet("color: #0066cc; font-size: 11px; font-weight: bold;")
        context_layout.addWidget(context_label)

        for file_path in self.context_files[:3]:  # Show max 3 files
            file_label = QLabel(f"• {os.path.basename(file_path)}")
            file_label.setStyleSheet("color: #b3b3b3; font-size: 10px; margin-left: 8px;")
            context_layout.addWidget(file_label)

        if len(self.context_files) > 3:
            more_label = QLabel(f"• ... and {len(self.context_files) - 3} more files")
            more_label.setStyleSheet("color: #808080; font-size: 10px; margin-left: 8px; font-style: italic;")
            context_layout.addWidget(more_label)

        layout.addWidget(context_frame)

    def _add_footer(self, layout: QVBoxLayout):
        """Add timestamp and action buttons"""
        footer_layout = QHBoxLayout()
        footer_layout.setContentsMargins(0, 4, 0, 0)

        # Timestamp
        self.time_label = QLabel(self.timestamp)
        self.time_label.setStyleSheet("color: #666666; font-size: 10px;")

        if self.is_user:
            footer_layout.addStretch()
            footer_layout.addWidget(self.time_label)
        else:
            footer_layout.addWidget(self.time_label)
            footer_layout.addStretch()

            # Add action buttons for AI messages
            self._add_action_buttons(footer_layout)

        layout.addLayout(footer_layout)

    def _add_action_buttons(self, layout: QHBoxLayout):
        """Add action buttons for AI messages"""
        # Copy button
        copy_btn = QPushButton("📋")
        copy_btn.setFixedSize(24, 24)
        copy_btn.setToolTip("Copy message")
        copy_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                border-radius: 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        copy_btn.clicked.connect(self._copy_message)

        # Regenerate button
        regen_btn = QPushButton("🔄")
        regen_btn.setFixedSize(24, 24)
        regen_btn.setToolTip("Regenerate response")
        regen_btn.setStyleSheet(copy_btn.styleSheet())

        layout.addWidget(copy_btn)
        layout.addWidget(regen_btn)

    def _copy_message(self):
        """Copy message to clipboard"""
        from PyQt6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(self.message)

    def _apply_bubble_style(self):
        """Apply Cursor-style bubble styling"""
        self.setStyleSheet(get_chat_bubble_style(self.is_user))

    def start_streaming(self):
        """Start streaming if content widget supports it"""
        if hasattr(self.content_widget, 'start_streaming_text') and self.enable_streaming:
            self.content_widget.start_streaming_text(self.message)

    def stop_streaming(self):
        """Stop streaming and show full content"""
        if hasattr(self.content_widget, 'stop_streaming'):
            self.content_widget.stop_streaming()

    def set_streaming_speed(self, speed: int):
        """Set streaming speed"""
        if hasattr(self.content_widget, 'set_streaming_speed'):
            self.content_widget.set_streaming_speed(speed)

    def append_text(self, text: str):
        """Append text to existing content (for real-time streaming)"""
        if hasattr(self.content_widget, 'start_streaming_text'):
            # For streaming widget, append to streaming text
            current_text = getattr(self.content_widget, 'streaming_text', '')
            self.content_widget.start_streaming_text(current_text + text)
        else:
            # For regular widget, append directly
            cursor = self.content_widget.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            cursor.insertText(text)


class ThinkingDisplay(QFrame):
    """Enhanced thinking display with Cursor-style animation and design"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.thinking_steps = []
        self.is_thinking = False
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._animate_thinking)
        self.animation_dots = 0

        self._setup_ui()

    def _setup_ui(self):
        """Setup enhanced thinking display UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(8)

        # Header with animated thinking indicator
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        # Thinking icon and label
        self.thinking_icon = QLabel("🧠")
        self.thinking_icon.setStyleSheet("font-size: 16px;")

        self.thinking_label = QLabel("AI is thinking")
        self.thinking_label.setStyleSheet("""
            QLabel {
                color: #0078d4;
                font-weight: bold;
                font-size: 13px;
            }
        """)

        # Animated dots
        self.dots_label = QLabel("...")
        self.dots_label.setStyleSheet("""
            QLabel {
                color: #0078d4;
                font-size: 13px;
                min-width: 20px;
            }
        """)

        # Collapse/expand button
        self.collapse_btn = QPushButton("▼")
        self.collapse_btn.setFixedSize(24, 24)
        self.collapse_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #888888;
                font-size: 12px;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #cccccc;
            }
        """)
        self.collapse_btn.clicked.connect(self._toggle_collapse)

        header_layout.addWidget(self.thinking_icon)
        header_layout.addWidget(self.thinking_label)
        header_layout.addWidget(self.dots_label)
        header_layout.addStretch()
        header_layout.addWidget(self.collapse_btn)

        # Progress indicator
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.progress_bar.setFixedHeight(3)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #333333;
                border-radius: 1px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 1px;
            }
        """)

        # Thinking steps container
        self.steps_scroll = QScrollArea()
        self.steps_scroll.setWidgetResizable(True)
        self.steps_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.steps_scroll.setMaximumHeight(250)
        self.steps_scroll.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
        """)

        self.steps_container = QWidget()
        self.steps_layout = QVBoxLayout(self.steps_container)
        self.steps_layout.setContentsMargins(0, 0, 0, 0)
        self.steps_layout.setSpacing(4)
        self.steps_layout.addStretch()

        self.steps_scroll.setWidget(self.steps_container)

        # Apply overall styling
        self.setStyleSheet("""
            QFrame {
                background-color: #1a1a1a;
                border: 1px solid #333333;
                border-radius: 8px;
            }
        """)

        layout.addLayout(header_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.steps_scroll)

        self.collapsed = False

    def start_thinking(self):
        """Start thinking animation"""
        self.is_thinking = True
        self.progress_bar.show()
        self.animation_timer.start(500)  # Update every 500ms

        # Update labels
        self.thinking_label.setText("AI is thinking")
        self.thinking_icon.setText("🧠")

    def stop_thinking(self):
        """Stop thinking animation"""
        self.is_thinking = False
        self.progress_bar.hide()
        self.animation_timer.stop()

        # Update labels
        self.thinking_label.setText("Thinking complete")
        self.thinking_icon.setText("✅")
        self.dots_label.setText("")

    def _animate_thinking(self):
        """Animate thinking dots"""
        if not self.is_thinking:
            return

        self.animation_dots = (self.animation_dots + 1) % 4
        dots = "." * self.animation_dots
        self.dots_label.setText(dots.ljust(3))

    def _toggle_collapse(self):
        """Toggle thinking display collapse with animation"""
        self.collapsed = not self.collapsed

        # Create smooth animation
        self.animation = QPropertyAnimation(self.steps_scroll, b"maximumHeight")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        if self.collapsed:
            self.animation.setStartValue(self.steps_scroll.height())
            self.animation.setEndValue(0)
            self.collapse_btn.setText("▶")
        else:
            self.animation.setStartValue(0)
            self.animation.setEndValue(250)
            self.collapse_btn.setText("▼")

        self.animation.start()

    def add_thinking_step(self, step: str, step_type: str = "info"):
        """Add thinking step with enhanced styling"""
        step_widget = QFrame()
        step_widget.setStyleSheet("""
            QFrame {
                background-color: #222222;
                border-radius: 6px;
                padding: 8px;
                margin: 2px 0px;
            }
        """)

        step_layout = QHBoxLayout(step_widget)
        step_layout.setContentsMargins(8, 6, 8, 6)
        step_layout.setSpacing(8)

        # Step icon based on type
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "thinking": "🤔",
            "code": "💻",
            "search": "🔍"
        }

        icon_label = QLabel(icons.get(step_type, "•"))
        icon_label.setFixedSize(16, 16)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Step text
        text_label = QLabel(step)
        text_label.setWordWrap(True)
        text_label.setStyleSheet("""
            QLabel {
                color: #e8e8e8;
                font-size: 12px;
                line-height: 1.4;
            }
        """)

        step_layout.addWidget(icon_label)
        step_layout.addWidget(text_label, 1)

        # Insert before stretch
        self.steps_layout.insertWidget(self.steps_layout.count() - 1, step_widget)

        # Auto-scroll to bottom
        QTimer.singleShot(50, self._scroll_to_bottom)

        self.thinking_steps.append(step)

    def _scroll_to_bottom(self):
        """Scroll to bottom of thinking steps"""
        scrollbar = self.steps_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_thinking(self):
        """Clear all thinking steps"""
        self.thinking_steps.clear()

        # Remove all step widgets except stretch
        while self.steps_layout.count() > 1:
            child = self.steps_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Reset state
        self.stop_thinking()


class ConversationHistory(QListWidget):
    """Conversation history widget"""
    
    conversation_selected = pyqtSignal(str)  # conversation_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.conversations = {}
        
        self._setup_ui()
        self._load_conversations()
    
    def _setup_ui(self):
        """Setup conversation history UI"""
        self.setStyleSheet("""
            QListWidget {
                background-color: #181818;
                color: #e8e8e8;
                border: none;
                outline: none;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #333333;
            }
            QListWidget::item:hover {
                background-color: #2a2a2a;
            }
            QListWidget::item:selected {
                background-color: #0066cc;
            }
        """)
        
        self.itemClicked.connect(self._on_item_clicked)
    
    def _load_conversations(self):
        """Load conversation history"""
        # TODO: Load from file or API
        pass
    
    def _on_item_clicked(self, item):
        """Handle conversation selection"""
        conversation_id = item.data(Qt.ItemDataRole.UserRole)
        if conversation_id:
            self.conversation_selected.emit(conversation_id)
    
    def add_conversation(self, conversation_id: str, title: str):
        """Add conversation to history"""
        item = QListWidgetItem(title)
        item.setData(Qt.ItemDataRole.UserRole, conversation_id)
        self.insertItem(0, item)  # Add to top
        
        # Limit history size
        while self.count() > 50:
            self.takeItem(self.count() - 1)


class ServerConnectionWidget(QWidget):
    """Server connection widget with IP input and status"""

    connection_changed = pyqtSignal(bool)  # Connected/disconnected
    models_updated = pyqtSignal(list)  # New models list

    def __init__(self, parent=None):
        super().__init__(parent)
        self.api_client = None
        self.is_connected = False
        self._setup_ui()

    def _setup_ui(self):
        """Setup server connection UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # Server connection button
        self.connect_btn = QPushButton()
        self.connect_btn.setFixedSize(24, 24)
        self.connect_btn.setToolTip("Connect to server")
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 12px;
                color: #666666;
                font-size: 14px;
                padding: 0;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        
        # Set initial icon
        self._update_button_icon(False)

        layout.addWidget(self.connect_btn)

        # Connect signals
        self.connect_btn.clicked.connect(self._toggle_connection)

    def _update_button_icon(self, connected):
        """Update button icon based on connection status"""
        if connected:
            # Connected icon (green dot)
            self.connect_btn.setText("●")
            self.connect_btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 12px;
                    color: #28a745;
                    font-size: 14px;
                    padding: 0;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
            """)
            self.connect_btn.setToolTip("Connected - Click to disconnect")
        else:
            # Disconnected icon (red dot)
            self.connect_btn.setText("●")
            self.connect_btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 12px;
                    color: #dc3545;
                    font-size: 14px;
                    padding: 0;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
            """)
            self.connect_btn.setToolTip("Disconnected - Click to connect")

    def _toggle_connection(self):
        """Toggle server connection"""
        if self.is_connected:
            self._disconnect()
        else:
            self._show_connection_dialog()

    def _show_connection_dialog(self):
        """Show connection dialog"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QSpinBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("Connect to Server")
        dialog.setMinimumWidth(350)
        
        layout = QVBoxLayout(dialog)
        
        # Server address
        host_layout = QHBoxLayout()
        host_label = QLabel("Server:")
        host_label.setMinimumWidth(60)
        
        self.host_input = QLineEdit()
        self.host_input.setText(config.get("server.host", "127.0.0.1"))
        
        host_layout.addWidget(host_label)
        host_layout.addWidget(self.host_input)
        
        # Port
        port_layout = QHBoxLayout()
        port_label = QLabel("Port:")
        port_label.setMinimumWidth(60)
        
        self.port_input = QSpinBox()
        self.port_input.setRange(1, 65535)
        self.port_input.setValue(config.get("server.port", 27027))
        
        port_layout.addWidget(port_label)
        port_layout.addWidget(self.port_input)
        
        # Status
        self.status_label = QLabel("Ready to connect")
        
        # Buttons
        button_layout = QHBoxLayout()
        connect_btn = QPushButton("Connect")
        cancel_btn = QPushButton("Cancel")
        
        button_layout.addStretch()
        button_layout.addWidget(connect_btn)
        button_layout.addWidget(cancel_btn)
        
        # Add to layout
        layout.addLayout(host_layout)
        layout.addLayout(port_layout)
        layout.addWidget(self.status_label)
        layout.addLayout(button_layout)
        
        # Connect signals
        connect_btn.clicked.connect(lambda: self._connect(
            self.host_input.text(),
            self.port_input.value(),
            dialog
        ))
        cancel_btn.clicked.connect(dialog.reject)
        
        # Show dialog
        dialog.exec()

    def _connect(self, host=None, port=None, dialog=None):
        """Connect to server"""
        try:
            from api.client import APIClient

            if host is None:
                host = config.get("server.host", "127.0.0.1")
            
            if port is None:
                port = config.get("server.port", 27027)
            
            # Construct full URL
            base_url = f"http://{host}:{port}"

            # Update status if dialog is open
            if dialog and hasattr(self, 'status_label'):
                self.status_label.setText("Connecting...")
                self.status_label.setStyleSheet("color: #ffc107;")
                dialog.repaint()  # Force UI update

            # Create API client
            self.api_client = APIClient(base_url)

            # Test connection with health check
            health_result = self.api_client.health_check()
            if not health_result.get("healthy", False) and health_result.get("status") != "healthy":
                error_msg = health_result.get("message", "Server health check failed")
                raise Exception(f"Server not healthy: {error_msg}")

            # Connection successful
            self.is_connected = True
            self._update_button_icon(True)
            
            # Update config
            config.set("server.host", host)
            config.set("server.port", port)
            config.save()

            # Fetch models
            self._fetch_models()

            self.connection_changed.emit(True)
            logger.info(f"Successfully connected to server: {base_url}")
            
            # Close dialog if open
            if dialog:
                dialog.accept()

        except Exception as e:
            self.is_connected = False
            self.api_client = None
            self._update_button_icon(False)
            
            error_msg = f"Connection failed: {str(e)}"
            logger.error(error_msg)
            
            # Update status if dialog is open
            if dialog and hasattr(self, 'status_label'):
                self.status_label.setText(f"Error: {str(e)}")
                self.status_label.setStyleSheet("color: #dc3545;")

    def _disconnect(self):
        """Disconnect from server"""
        self.is_connected = False
        self.api_client = None
        self._update_button_icon(False)

        self.connection_changed.emit(False)
        self.models_updated.emit([])

    def _fetch_models(self):
        """Fetch available models from server"""
        if not self.api_client:
            return

        try:
            logger.info("Fetching models from server...")
            response = self.api_client.session.get(
                f"{self.api_client.base_url}/api/models/available",
                timeout=15
            )
            response.raise_for_status()
            data = response.json()

            if data.get("success"):
                models = data.get("models", [])
                logger.info(f"Successfully fetched {len(models)} models")
                self.models_updated.emit(models)
            else:
                error_msg = data.get("error", "Unknown error")
                logger.error(f"Failed to fetch models: {error_msg}")
                # Still emit empty list to update UI
                self.models_updated.emit([])

        except Exception as e:
            logger.error(f"Error fetching models: {e}")
            self.models_updated.emit([])


class ModelSelector(QComboBox):
    """Enhanced model selector with server connection"""

    model_changed = pyqtSignal(str)  # Model ID changed

    def __init__(self, parent=None):
        super().__init__(parent)
        self.models_data = []
        self.api_client = None

        self.setStyleSheet(get_model_selector_style())
        
        # Set fixed height for better appearance
        self.setFixedHeight(30)

        # Add default Qwen models
        self.addItem("🚀 Qwen/Qwen1.5-4B (Recommended)", "Qwen/Qwen1.5-4B")
        self.addItem("⚡ Qwen/Qwen1.5-7B", "Qwen/Qwen1.5-7B")
        self.addItem("🔥 Qwen/Qwen1.5-14B", "Qwen/Qwen1.5-14B")
        
        # Set tooltip
        self.setToolTip("Select AI model")

        # Connect selection change
        self.currentIndexChanged.connect(self._on_selection_changed)

    def set_api_client(self, api_client):
        """Set API client for model operations"""
        self.api_client = api_client
        
        # Fetch models if client is available
        if self.api_client:
            try:
                models_response = self.api_client.get_models()
                if models_response.get("success") and "models" in models_response:
                    self.update_models(models_response["models"])
            except Exception as e:
                logger.warning(f"Failed to fetch models: {e}")

    def update_models(self, models: list):
        """Update models list from server"""
        self.clear()
        self.models_data = models

        if not models:
            # Fallback to default Qwen models
            self.addItem("🚀 Qwen/Qwen1.5-4B (Recommended)", "Qwen/Qwen1.5-4B")
            self.addItem("⚡ Qwen/Qwen1.5-7B", "Qwen/Qwen1.5-7B")
            self.addItem("🔥 Qwen/Qwen1.5-14B", "Qwen/Qwen1.5-14B")
            return

        # Group models by type
        local_models = [m for m in models if m.get("type") == "local"]
        hf_models = [m for m in models if m.get("type") == "huggingface"]

        # Add HuggingFace models first (Qwen models)
        if hf_models:
            for model in hf_models:
                name = model.get('name', 'Unknown')
                display_name = name

                # Add emoji and status indicators
                if 'Qwen1.5-4B' in name:
                    display_name = f"🚀 {name} (Recommended)"
                elif 'Qwen1.5-7B' in name:
                    display_name = f"⚡ {name}"
                elif 'Qwen1.5-14B' in name:
                    display_name = f"🔥 {name}"
                else:
                    display_name = f"🤖 {name}"

                self.addItem(display_name, model.get("id", name))

        # Add local models if any
        if local_models:
            if hf_models:  # Add separator if we have both types
                self.addItem("─── Local Models ───", "separator")
            for model in local_models:
                name = model.get('name', 'Unknown')
                display_name = f"💾 {name} (Local)"
                self.addItem(display_name, model.get("id", name))

        # Select first available model (skip separator)
        if len(models) > 0:
            for i in range(self.count()):
                if self.itemData(i) != "separator":
                    self.setCurrentIndex(i)
                    break

        # Set tooltip with model info
        self._update_tooltip()

    def _on_selection_changed(self, index):
        """Handle model selection change"""
        current_data = self.itemData(index)
        if current_data:
            self.model_changed.emit(current_data)
            self._update_tooltip()

    def _update_tooltip(self):
        """Update tooltip with current model info"""
        current_model = self.get_selected_model()
        if current_model:
            model_info = f"Model: {current_model['name']}\n"
            if 'description' in current_model:
                model_info += f"Description: {current_model['description']}\n"
            if 'size' in current_model:
                model_info += f"Size: {current_model['size']}\n"
            self.setToolTip(model_info)

    def get_selected_model(self):
        """Get currently selected model data"""
        model_id = self.currentData()
        if model_id:
            for model in self.models_data:
                if model.get("id") == model_id or model.get("name") == model_id:
                    return model
        return None


class ChatTabWidget(QWidget):
    """Individual chat tab widget"""

    def __init__(self, tab_id: str, title: str = "New Chat", parent=None):
        super().__init__(parent)
        self.tab_id = tab_id
        self.title = title
        self.messages = []
        self.context_files = []

        self._setup_ui()

    def _setup_ui(self):
        """Setup chat tab UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Chat display area
        self.chat_scroll = QScrollArea()
        self.chat_scroll.setWidgetResizable(True)
        self.chat_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.chat_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #181818;
                border: none;
            }
        """)

        self.chat_container = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setContentsMargins(8, 8, 8, 8)
        self.chat_layout.setSpacing(8)
        self.chat_layout.addStretch()  # Push messages to bottom

        self.chat_scroll.setWidget(self.chat_container)
        layout.addWidget(self.chat_scroll)

    def add_message(self, message: str, is_user: bool = True, message_type: str = "text",
                   enable_streaming: bool = False):
        """Add message to chat with optional streaming"""
        bubble = ChatBubble(
            message=message,
            is_user=is_user,
            message_type=message_type,
            context_files=self.context_files if not is_user else None,
            enable_streaming=enable_streaming
        )

        # Insert before stretch
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, bubble)

        # Auto-scroll to bottom
        QTimer.singleShot(100, self._scroll_to_bottom)

        # Store message
        self.messages.append({
            'message': message,
            'is_user': is_user,
            'timestamp': datetime.now().isoformat(),
            'type': message_type
        })

        return bubble  # Return bubble for streaming control

    def add_streaming_message(self, initial_text: str = "", message_type: str = "text"):
        """Add a message that will be streamed in real-time"""
        bubble = self.add_message(
            message=initial_text,
            is_user=False,
            message_type=message_type,
            enable_streaming=True
        )
        return bubble

    def _scroll_to_bottom(self):
        """Scroll chat to bottom"""
        scrollbar = self.chat_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_chat(self):
        """Clear all messages"""
        # Remove all message widgets except stretch
        while self.chat_layout.count() > 1:
            child = self.chat_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        self.messages.clear()


class AISidebar(QWidget):
    """Enhanced AI interaction sidebar with Cursor-style design and streaming support"""

    message_sent = pyqtSignal(str, str, str)  # message, mode, tab_id
    file_context_requested = pyqtSignal(str)  # file_path
    new_chat_requested = pyqtSignal()

    def __init__(self, api_client: APIClient, parent=None):
        super().__init__(parent)
        self.api_client = api_client
        self.current_mode = "chat"  # chat, agent, composer
        self.context_files = []
        self.chat_tabs = {}
        self.current_tab_id = None
        self.tab_counter = 0
        self.current_streaming_bubble = None  # Track current streaming message
        self.streaming_enabled = True  # Global streaming toggle

        self._setup_ui()
        self._connect_signals()
        self._load_models()
        self._create_new_chat()  # Create initial chat

    def _setup_ui(self):
        """Setup UI components"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Apply sidebar style
        self.setStyleSheet(get_sidebar_style())
        
        # Initialize tabs and tab_stack
        self.tabs = {}
        self.tab_stack = QStackedWidget()
        
        # Create header
        self._create_header(main_layout)
        
        # Create chat tabs
        self._create_chat_tabs(main_layout)
        
        # Create chat area
        self._create_chat_area(main_layout)
        
        # Create input section
        self._create_input_section(main_layout)
        
        # Initialize state
        self.current_tab_id = "default"
        self.tabs[self.current_tab_id] = ChatTabWidget(self.current_tab_id, "New Chat", self)
        self.tab_stack.addWidget(self.tabs[self.current_tab_id])
        
        # Set focus to input
        QTimer.singleShot(100, lambda: self.message_input.setFocus() if hasattr(self, 'message_input') else None)

    def _create_header(self, parent_layout: QVBoxLayout):
        """Create header section with model selector and mode buttons"""
        header_frame = QFrame()
        header_frame.setObjectName("ai_header")
        header_frame.setStyleSheet(get_header_style())
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 10, 10, 5)
        header_layout.setSpacing(8)
        
        # Title and model selector row
        title_row = QHBoxLayout()
        title_row.setContentsMargins(0, 0, 0, 0)
        title_row.setSpacing(10)
        
        # Title with Cursor-style design
        title_label = QLabel("🤖 AI Assistant")
        title_label.setObjectName("ai_title")
        title_label.setFont(QFont("Segoe UI", 13, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                padding: 2px 0px;
            }
        """)
        title_row.addWidget(title_label)
        
        title_row.addStretch(1)
        
        # Server connection widget (small icon button)
        self.connection_widget = ServerConnectionWidget(self)
        self.connection_widget.setFixedHeight(30)
        self.connection_widget.setMaximumWidth(30)
        title_row.addWidget(self.connection_widget)
        
        header_layout.addLayout(title_row)
        
        # Model selector
        model_row = QHBoxLayout()
        model_row.setContentsMargins(0, 0, 0, 0)
        model_row.setSpacing(8)
        
        model_label = QLabel("Model:")
        model_label.setObjectName("model_label")
        model_row.addWidget(model_label)
        
        self.model_selector = ModelSelector(self)
        self.model_selector.setObjectName("model_selector")
        self.model_selector.setFixedHeight(30)
        model_row.addWidget(self.model_selector, 1)
        
        header_layout.addLayout(model_row)
        
        # Mode selection buttons
        mode_row = QHBoxLayout()
        mode_row.setContentsMargins(0, 0, 0, 0)
        mode_row.setSpacing(0)
        
        self.mode_group = QButtonGroup(self)
        
        # Chat mode
        self.chat_mode_btn = QPushButton("💬 Chat")
        self.chat_mode_btn.setObjectName("mode_button")
        self.chat_mode_btn.setCheckable(True)
        self.chat_mode_btn.setChecked(True)
        self.chat_mode_btn.setToolTip("Chat with AI assistant")
        self.mode_group.addButton(self.chat_mode_btn)
        mode_row.addWidget(self.chat_mode_btn)

        # Agent mode
        self.agent_mode_btn = QPushButton("🤖 Agent")
        self.agent_mode_btn.setObjectName("mode_button")
        self.agent_mode_btn.setCheckable(True)
        self.agent_mode_btn.setToolTip("AI agent for autonomous tasks")
        self.mode_group.addButton(self.agent_mode_btn)
        mode_row.addWidget(self.agent_mode_btn)

        # Composer mode
        self.composer_mode_btn = QPushButton("✨ Composer")
        self.composer_mode_btn.setObjectName("mode_button")
        self.composer_mode_btn.setCheckable(True)
        self.composer_mode_btn.setToolTip("AI-powered code generation")
        self.mode_group.addButton(self.composer_mode_btn)
        mode_row.addWidget(self.composer_mode_btn)
        
        header_layout.addLayout(mode_row)
        
        # Context display (collapsible)
        self.context_display = QFrame()
        self.context_display.setObjectName("context_display")
        context_layout = QVBoxLayout(self.context_display)
        context_layout.setContentsMargins(5, 5, 5, 5)
        context_layout.setSpacing(2)
        
        context_header = QHBoxLayout()
        context_label = QLabel("Context Files:")
        context_label.setObjectName("context_label")
        context_header.addWidget(context_label)
        
        context_header.addStretch(1)
        
        clear_context_btn = QPushButton("Clear")
        clear_context_btn.setObjectName("small_button")
        clear_context_btn.setFixedSize(60, 24)
        clear_context_btn.clicked.connect(self._clear_context)
        context_header.addWidget(clear_context_btn)
        
        context_layout.addLayout(context_header)
        
        # Context files list
        self.context_files_list = QListWidget()
        self.context_files_list.setObjectName("context_files_list")
        self.context_files_list.setMaximumHeight(100)
        context_layout.addWidget(self.context_files_list)
        
        # Initially hide context display
        self.context_display.hide()
        header_layout.addWidget(self.context_display)
        
        parent_layout.addWidget(header_frame)

    def _create_chat_tabs(self, parent_layout: QVBoxLayout):
        """Create chat tabs section"""
        tabs_frame = QFrame()
        tabs_frame.setStyleSheet("""
            QFrame {
                background-color: #1a1a1a;
                border-bottom: 1px solid #333333;
                max-height: 40px;
            }
        """)

        tabs_layout = QHBoxLayout(tabs_frame)
        tabs_layout.setContentsMargins(8, 4, 8, 4)
        tabs_layout.setSpacing(4)

        # Chat tabs container (scrollable)
        self.tabs_scroll = QScrollArea()
        self.tabs_scroll.setWidgetResizable(True)
        self.tabs_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.tabs_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.tabs_scroll.setFixedHeight(32)
        self.tabs_scroll.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
        """)

        self.tabs_container = QWidget()
        self.tabs_layout = QHBoxLayout(self.tabs_container)
        self.tabs_layout.setContentsMargins(0, 0, 0, 0)
        self.tabs_layout.setSpacing(4)
        self.tabs_layout.addStretch()

        self.tabs_scroll.setWidget(self.tabs_container)

        # New chat button
        self.new_chat_btn = QPushButton("+")
        self.new_chat_btn.setFixedSize(24, 24)
        self.new_chat_btn.setToolTip("New Chat")
        self.new_chat_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: 1px solid #333333;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #333333;
                color: white;
            }
        """)
        self.new_chat_btn.clicked.connect(self._create_new_chat)

        tabs_layout.addWidget(self.tabs_scroll, 1)
        tabs_layout.addWidget(self.new_chat_btn)

        parent_layout.addWidget(tabs_frame)
        
        # Initialize tabs dict
        self.tabs = {}

    def _create_chat_area(self, parent_layout: QVBoxLayout):
        """Create main chat area"""
        # Stack widget to hold different chat tabs
        self.chat_stack = QStackedWidget()
        self.chat_stack.setStyleSheet("""
            QStackedWidget {
                background-color: #181818;
            }
        """)

        parent_layout.addWidget(self.chat_stack, 1)  # Give it stretch factor

    def _create_input_section(self, parent_layout: QVBoxLayout):
        """Create input section fixed at bottom"""
        input_frame = QFrame()
        input_frame.setStyleSheet(get_input_area_style())

        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(12, 8, 12, 8)
        input_layout.setSpacing(8)

        # Context files display
        self.context_display = QFrame()
        self.context_display.setStyleSheet("""
            QFrame {
                background-color: #222222;
                border: 1px solid #333333;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        self.context_display.hide()  # Initially hidden

        context_layout = QHBoxLayout(self.context_display)
        context_layout.setContentsMargins(8, 4, 8, 4)
        context_layout.setSpacing(8)

        self.context_label = QLabel("📁 Context files:")
        self.context_label.setStyleSheet("color: #b3b3b3; font-size: 11px;")

        self.context_files_label = QLabel("None")
        self.context_files_label.setStyleSheet("color: #e8e8e8; font-size: 11px;")

        context_clear_btn = QPushButton("✕")
        context_clear_btn.setFixedSize(20, 20)
        context_clear_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #888888;
                font-size: 12px;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #cccccc;
            }
        """)
        context_clear_btn.clicked.connect(self._clear_context)

        context_layout.addWidget(self.context_label)
        context_layout.addWidget(self.context_files_label, 1)
        context_layout.addWidget(context_clear_btn)

        # Message input area
        input_container = QFrame()
        input_container.setStyleSheet("""
            QFrame {
                background-color: #222222;
                border: 1px solid #333333;
                border-radius: 12px;
            }
        """)

        input_container_layout = QVBoxLayout(input_container)
        input_container_layout.setContentsMargins(1, 1, 1, 1)
        input_container_layout.setSpacing(0)

        # Message input text area with auto-expansion
        self.message_input = ExpandableTextEdit()
        self.message_input.setPlaceholderText("Ask AI anything... (Shift+Enter for new line, Enter to send)")
        self.message_input.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                color: #ffffff;
                padding: 10px 12px;
                font-size: 13px;
                line-height: 1.4;
            }
        """)

        # Input toolbar
        toolbar = QFrame()
        toolbar.setStyleSheet("""
            QFrame {
                background-color: #1a1a1a;
                border-top: 1px solid #333333;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
            }
        """)

        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(8, 6, 8, 6)
        toolbar_layout.setSpacing(6)

        # Left side buttons
        self.attach_btn = QPushButton("📎")
        self.attach_btn.setFixedSize(28, 28)
        self.attach_btn.setToolTip("Attach files to context")
        self.attach_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 14px;
                color: #999999;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #ffffff;
            }
        """)

        self.context_btn = QPushButton("@")
        self.context_btn.setFixedSize(28, 28)
        self.context_btn.setToolTip("Reference files or symbols")
        self.context_btn.setStyleSheet(self.attach_btn.styleSheet())

        # Right side buttons
        self.send_btn = QPushButton("Send")
        self.send_btn.setFixedSize(60, 28)
        self.send_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                padding: 4px 12px;
            }
            QPushButton:hover {
                background-color: #0066b5;
            }
            QPushButton:pressed {
                background-color: #005494;
            }
            QPushButton:disabled {
                background-color: #333333;
                color: #666666;
            }
        """)

        # Character count
        self.char_count = QLabel("0")
        self.char_count.setStyleSheet("color: #808080; font-size: 10px;")

        toolbar_layout.addWidget(self.attach_btn)
        toolbar_layout.addWidget(self.context_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.char_count)
        toolbar_layout.addWidget(self.send_btn)

        input_container_layout.addWidget(self.message_input)
        input_container_layout.addWidget(toolbar)

        input_layout.addWidget(self.context_display)
        input_layout.addWidget(input_container)

        parent_layout.addWidget(input_frame)

    def _create_new_chat(self):
        """Create a new chat tab"""
        self.tab_counter += 1
        tab_id = f"chat_{self.tab_counter}"
        title = f"Chat {self.tab_counter}"

        # Create chat tab widget
        chat_tab = ChatTabWidget(tab_id, title)
        self.chat_tabs[tab_id] = chat_tab

        # Add to stack
        self.chat_stack.addWidget(chat_tab)
        self.chat_stack.setCurrentWidget(chat_tab)
        self.current_tab_id = tab_id

        # Create tab button
        self._create_tab_button(tab_id, title)

        return tab_id

    def _create_tab_button(self, tab_id: str, title: str):
        """Create a tab button for chat tab"""
        tab_btn = QPushButton(title)
        tab_btn.setCheckable(True)
        tab_btn.setChecked(True)  # New tab is active
        tab_btn.setFixedHeight(24)
        tab_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                border-radius: 4px;
                padding: 4px 10px;
                font-size: 12px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #2a2a2a;
                color: #ffffff;
            }
            QPushButton:checked {
                background-color: #2a2a2a;
                color: #ffffff;
                font-weight: bold;
            }
        """)

        # Connect signals
        tab_btn.clicked.connect(lambda: self._switch_to_tab(tab_id))

        # Add close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(16, 16)
        close_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #666666;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 0;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #ffffff;
            }
        """)
        close_btn.clicked.connect(lambda: self._close_tab(tab_id))

        # Create container for tab button and close button
        tab_container = QFrame()
        tab_container.setStyleSheet("QFrame { background: transparent; }")
        tab_layout = QHBoxLayout(tab_container)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(2)
        tab_layout.addWidget(tab_btn)
        tab_layout.addWidget(close_btn)

        # Insert before stretch
        self.tabs_layout.insertWidget(self.tabs_layout.count() - 1, tab_container)

        # Uncheck other tab buttons
        for i in range(self.tabs_layout.count() - 1):
            item = self.tabs_layout.itemAt(i)
            if item and item.widget() and item.widget() != tab_container:
                container = item.widget()
                if hasattr(container, 'layout'):
                    for j in range(container.layout().count()):
                        widget = container.layout().itemAt(j).widget()
                        if isinstance(widget, QPushButton) and widget.isCheckable():
                            widget.setChecked(False)

    def _switch_to_tab(self, tab_id: str):
        """Switch to specified chat tab"""
        if tab_id in self.chat_tabs:
            self.current_tab_id = tab_id
            self.chat_stack.setCurrentWidget(self.chat_tabs[tab_id])

            # Update context files for current tab
            current_tab = self.chat_tabs[tab_id]
            self.context_files = current_tab.context_files
            self._update_context_display()

    def _close_tab(self, tab_id: str):
        """Close specified chat tab"""
        if len(self.chat_tabs) <= 1:
            return  # Don't close last tab

        if tab_id in self.chat_tabs:
            # Remove from stack
            widget = self.chat_tabs[tab_id]
            self.chat_stack.removeWidget(widget)
            widget.deleteLater()
            del self.chat_tabs[tab_id]

            # Remove tab button
            for i in range(self.tabs_layout.count()):
                item = self.tabs_layout.itemAt(i)
                if item and item.widget():
                    # This is a simplified approach - in practice you'd need to track which button belongs to which tab
                    pass

            # Switch to another tab if this was current
            if self.current_tab_id == tab_id:
                remaining_tabs = list(self.chat_tabs.keys())
                if remaining_tabs:
                    self._switch_to_tab(remaining_tabs[0])

    def _clear_context(self):
        """Clear context files"""
        self.context_files.clear()
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            self.chat_tabs[self.current_tab_id].context_files.clear()
        self._update_context_display()

    def _update_context_display(self):
        """Update context files display"""
        if self.context_files:
            file_names = [os.path.basename(f) for f in self.context_files[:3]]
            if len(self.context_files) > 3:
                file_names.append(f"... +{len(self.context_files) - 3} more")

            self.context_files_label.setText(", ".join(file_names))
            self.context_display.show()
        else:
            self.context_display.hide()

    def _connect_signals(self):
        """Connect widget signals"""
        # API client signals
        self.api_client.connection_status_changed.connect(self._on_connection_changed)
        self.api_client.chat_response_received.connect(self._on_chat_response)
        self.api_client.agent_response_received.connect(self._on_agent_response)
        self.api_client.error_occurred.connect(self._on_error)

        # UI signals
        self.send_btn.clicked.connect(self._send_message)
        self.attach_btn.clicked.connect(self._attach_current_file)
        self.context_btn.clicked.connect(self._show_context_menu)
        self.message_input.textChanged.connect(self._on_input_changed)

        # Mode selector
        self.mode_group.buttonClicked.connect(self._on_mode_changed)

        # Model selector
        self.model_selector.model_changed.connect(self._on_model_changed)

        # Input handling
        self.message_input.installEventFilter(self)

        # Character count update
        self.message_input.textChanged.connect(self._update_char_count)

    def _load_models(self):
        """Load available models - now handled by server connection"""
        # Models are now loaded via server connection
        pass

    def _on_connection_changed(self, connected: bool):
        """Handle server connection change"""
        if connected:
            # Update API client
            self.api_client = self.connection_widget.api_client
            self.model_selector.set_api_client(self.api_client)
        else:
            # Clear API client
            self.api_client = None
            self.model_selector.set_api_client(None)

    def _on_models_updated(self, models: list):
        """Handle models list update from server"""
        self.model_selector.update_models(models)

    # Streaming support methods
    def add_user_message(self, message: str):
        """Add user message to current chat"""
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            current_tab = self.chat_tabs[self.current_tab_id]
            current_tab.add_message(message, is_user=True)

    def add_ai_message(self, message: str, message_type: str = "text", enable_streaming: bool = None):
        """Add AI message to current chat with optional streaming"""
        if enable_streaming is None:
            enable_streaming = self.streaming_enabled

        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            current_tab = self.chat_tabs[self.current_tab_id]
            bubble = current_tab.add_message(
                message,
                is_user=False,
                message_type=message_type,
                enable_streaming=enable_streaming
            )
            return bubble
        return None

    def start_streaming_response(self, message_type: str = "text"):
        """Start a new streaming AI response"""
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            current_tab = self.chat_tabs[self.current_tab_id]
            self.current_streaming_bubble = current_tab.add_streaming_message("", message_type)
            return self.current_streaming_bubble
        return None

    def append_to_streaming_response(self, text: str):
        """Append text to current streaming response"""
        if self.current_streaming_bubble:
            self.current_streaming_bubble.append_text(text)

    def finish_streaming_response(self):
        """Finish current streaming response"""
        if self.current_streaming_bubble:
            self.current_streaming_bubble.stop_streaming()
            self.current_streaming_bubble = None

    def set_streaming_enabled(self, enabled: bool):
        """Enable or disable streaming globally"""
        self.streaming_enabled = enabled

    def set_streaming_speed(self, speed: int):
        """Set global streaming speed"""
        if self.current_streaming_bubble:
            self.current_streaming_bubble.set_streaming_speed(speed)

    def _on_model_changed(self, model_id: str):
        """Handle model selection change"""
        _ = model_id  # Unused parameter
        if not self.api_client:
            return

        selected_model = self.model_selector.get_selected_model()
        if selected_model:
            # Load model on server
            self._load_model_on_server(selected_model)

    def _get_current_mode(self) -> str:
        """Get current mode from mode buttons"""
        if self.chat_mode_btn.isChecked():
            return "chat"
        elif self.agent_mode_btn.isChecked():
            return "agent"
        elif self.composer_mode_btn.isChecked():
            return "composer"
        return "chat"

    def _load_model_on_server(self, model_data: dict):
        """Load selected model on server"""
        try:
            model_name = model_data.get("name", "")
            if not model_name:
                logger.error("No model name provided")
                return

            logger.info(f"Loading model on server: {model_name}")

            # Use the API client's load_model method
            result = self.api_client.load_model(model_name)

            if result.get("success"):
                logger.info(f"Model {model_name} loaded successfully")
                # Update model selector to show loaded status
                self._refresh_models_status()
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"Failed to load model {model_name}: {error_msg}")

        except Exception as e:
            logger.error(f"Error loading model: {e}")

    def _refresh_models_status(self):
        """Refresh models to update their status"""
        if self.api_client:
            # Re-fetch models to get updated status
            self._fetch_models()

    def _on_mode_changed(self, button):
        """Handle mode change"""
        mode_map = {
            self.chat_mode_btn: "chat",
            self.agent_mode_btn: "agent",
            self.composer_mode_btn: "composer"
        }

        mode = mode_map.get(button, "chat")
        self.current_mode = mode

        # Update UI based on mode
        if mode == "agent":
            self.message_input.setPlaceholderText(
                "Describe the task you want the AI agent to perform..."
            )
            # Only show thinking display if it exists
            if hasattr(self, 'thinking_display'):
                self.thinking_display.show()
        elif mode == "composer":
            self.message_input.setPlaceholderText(
                "Describe what you want to create or modify..."
            )
            # Only hide thinking display if it exists
            if hasattr(self, 'thinking_display'):
                self.thinking_display.hide()
        else:  # chat
            self.message_input.setPlaceholderText(
                "Ask AI anything... (Shift+Enter for new line, Enter to send)"
            )
            # Only hide thinking display if it exists
            if hasattr(self, 'thinking_display'):
                self.thinking_display.hide()

        logger.info(f"AI mode set to: {mode}")

    def _on_connection_status_changed(self, connected: bool):
        """Handle connection status change"""
        # Skip if UI elements don't exist yet
        if not hasattr(self, 'status_indicator') or not hasattr(self, 'server_label'):
            return
            
        if connected:
            self.status_indicator.setStyleSheet("color: #28a745; font-size: 12px;")  # Green
            self.status_indicator.setToolTip("Connected")
            server_url = self.api_client.base_url
            self.server_label.setText(f"Connected to {server_url}")
            self._load_models()  # Reload models when connected
        else:
            self.status_indicator.setStyleSheet("color: #dc3545; font-size: 12px;")  # Red
            self.status_indicator.setToolTip("Disconnected")
            self.server_label.setText("Not connected")

    def _update_char_count(self):
        """Update character count display"""
        text = self.message_input.toPlainText()
        count = len(text)
        self.char_count.setText(str(count))

        # Change color based on length
        if count > 2000:
            self.char_count.setStyleSheet("color: #dc3545; font-size: 10px;")  # Red
        elif count > 1500:
            self.char_count.setStyleSheet("color: #ffc107; font-size: 10px;")  # Yellow
        else:
            self.char_count.setStyleSheet("color: #666666; font-size: 10px;")  # Gray

    def eventFilter(self, obj, event):
        """Handle key events for message input"""
        if obj == self.message_input and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
                if event.modifiers() == Qt.KeyboardModifier.ShiftModifier:
                    # Shift+Enter: new line
                    return False
                else:
                    # Enter: send message
                    self._send_message()
                    return True
        return super().eventFilter(obj, event)

    def _send_message(self):
        """Send message to AI"""
        message_text = self.message_input.toPlainText().strip()
        if not message_text or not self.current_tab_id:
            return

        # Add user message to current chat tab
        current_tab = self.chat_tabs[self.current_tab_id]
        current_tab.add_message(message_text, is_user=True)

        # Clear input
        self.message_input.clear()
        self._update_char_count()

        # Disable send button
        self.send_btn.setEnabled(False)
        self.send_btn.setText("Sending...")

        # Prepare context
        context = self._prepare_context()

        # Send to appropriate service
        if self.current_mode == "chat":
            self._send_chat_message_streaming(message_text, context)
        elif self.current_mode == "agent":
            self._send_agent_task(message_text, context)
        else:  # composer
            self._send_composer_task(message_text, context)

    def _send_chat_message(self, message: str, context: Dict[str, Any]):
        """Send chat message"""
        try:
            chat_message = ChatMessage(
                message=message,
                conversation_id=self.current_tab_id,
                context=context
            )

            # Send via API client (this will emit signals when response is received)
            self.api_client.send_chat_message(chat_message)

        except Exception as e:
            logger.error(f"Error sending chat message: {e}")
            self._on_error(f"Failed to send message: {str(e)}")

    def _send_chat_message_streaming(self, message: str, context: Dict[str, Any]):
        """Send chat message with streaming response"""
        try:
            chat_message = ChatMessage(
                message=message,
                conversation_id=self.current_tab_id,
                context=context
            )

            # Start streaming response bubble
            streaming_bubble = self.start_streaming_response("text")

            def handle_stream_chunk(chunk_data: Dict[str, Any]):
                """Handle streaming response chunks"""
                chunk_type = chunk_data.get('type', '')

                if chunk_type == 'response_chunk':
                    # Append text to streaming bubble
                    content = chunk_data.get('content', '')
                    if streaming_bubble:
                        streaming_bubble.append_text(content)

                elif chunk_type == 'response_complete':
                    # Finish streaming
                    self.finish_streaming_response()
                    # Re-enable send button
                    self.send_btn.setEnabled(True)
                    self.send_btn.setText("Send")

                elif chunk_type == 'error':
                    # Handle error
                    error_msg = chunk_data.get('message', 'Unknown error')
                    self._on_error(f"Streaming error: {error_msg}")
                    self.finish_streaming_response()
                    # Re-enable send button
                    self.send_btn.setEnabled(True)
                    self.send_btn.setText("Send")

                elif chunk_type == 'done':
                    # Streaming complete
                    self.finish_streaming_response()
                    # Re-enable send button
                    self.send_btn.setEnabled(True)
                    self.send_btn.setText("Send")

            # Send via API client with streaming
            result = self.api_client.send_chat_message_stream(chat_message, handle_stream_chunk)

            # If streaming failed, fall back to regular message
            if result.get('error'):
                self.finish_streaming_response()
                self._send_chat_message(message, context)

        except Exception as e:
            logger.error(f"Error sending streaming chat message: {e}")
            self.finish_streaming_response()
            self._on_error(f"Failed to send streaming message: {str(e)}")
            # Re-enable send button
            self.send_btn.setEnabled(True)
            self.send_btn.setText("Send")

    def _send_agent_task(self, task: str, context: Dict[str, Any]):
        """Send agent task"""
        try:
            # Show thinking display
            self.thinking_display.show()
            self.thinking_display.start_thinking()
            self.thinking_display.add_thinking_step("Analyzing task...", "thinking")

            agent_task = AgentTask(
                task=task,
                context=context,
                project_path=context.get('project_path'),
                max_iterations=10
            )

            # Send via API client
            self.api_client.send_agent_task(agent_task)

        except Exception as e:
            logger.error(f"Error sending agent task: {e}")
            self._on_error(f"Failed to send task: {str(e)}")

    def _send_composer_task(self, task: str, context: Dict[str, Any]):
        """Send composer task"""
        try:
            # Composer is similar to agent but focused on code generation
            composer_task = AgentTask(
                task=f"[COMPOSER MODE] {task}",
                context=context,
                project_path=context.get('project_path'),
                max_iterations=5
            )

            # Send via API client
            self.api_client.send_agent_task(composer_task)

        except Exception as e:
            logger.error(f"Error sending composer task: {e}")
            self._on_error(f"Failed to send composer task: {str(e)}")

    def _prepare_context(self) -> Dict[str, Any]:
        """Prepare context for AI request"""
        context = {
            'files': [],
            'project_path': os.getcwd(),
            'mode': self.current_mode,
            'model': self.model_selector.currentText()
        }

        # Add context files
        for file_path in self.context_files:
            try:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    context['files'].append({
                        'path': file_path,
                        'content': content,
                        'name': os.path.basename(file_path)
                    })
            except Exception as e:
                logger.warning(f"Failed to read context file {file_path}: {e}")

        return context

    def _attach_current_file(self):
        """Attach current file to context"""
        # This would be connected to the main window to get current file
        self.file_context_requested.emit("current")

    def _show_context_menu(self):
        """Show context menu for file references"""
        menu = QMenu(self)

        # Add current file action
        current_action = QAction("📄 Add Current File", self)
        current_action.triggered.connect(self._attach_current_file)
        menu.addAction(current_action)

        # Browse files action
        browse_action = QAction("📂 Browse Files...", self)
        browse_action.triggered.connect(self._browse_files)
        menu.addAction(browse_action)

        # Show menu at cursor
        menu.exec(self.mapToGlobal(self.context_btn.pos()))

    def _browse_files(self):
        """Browse and select files to add to context"""
        from PyQt6.QtWidgets import QFileDialog

        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Files for Context",
            os.getcwd(),
            "All Files (*.*)"
        )

        for file_path in files:
            if file_path not in self.context_files:
                self.context_files.append(file_path)
                if self.current_tab_id and self.current_tab_id in self.chat_tabs:
                    self.chat_tabs[self.current_tab_id].context_files.append(file_path)

        self._update_context_display()

    def _on_input_changed(self):
        """Handle input text changes"""
        # Auto-resize input based on content
        text = self.message_input.toPlainText()
        lines = text.count('\n') + 1

        # Calculate height based on lines (with limits)
        line_height = 20
        min_height = 40
        max_height = 120

        new_height = max(min_height, min(max_height, lines * line_height + 20))
        self.message_input.setMaximumHeight(new_height)
        self.message_input.setMinimumHeight(min_height)

    def _on_model_changed(self, model_name: str):
        """Handle model selection change"""
        logger.info(f"Model changed to: {model_name}")
        # Update config
        config.set('ai.default_model', model_name)

    def _on_chat_response(self, response: Dict[str, Any]):
        """Handle chat response with streaming support"""
        try:
            # Re-enable send button
            self.send_btn.setEnabled(True)
            self.send_btn.setText("Send")

            # Add AI response to current chat tab with streaming
            ai_message = response.get('response', 'No response received')
            if self.current_tab_id and self.current_tab_id in self.chat_tabs:
                # Use streaming for AI responses
                self.add_ai_message(ai_message, "text", enable_streaming=True)

        except Exception as e:
            logger.error(f"Error handling chat response: {e}")
            self._on_error(f"Error processing response: {str(e)}")

    def _on_agent_response(self, response: Dict[str, Any]):
        """Handle agent response"""
        try:
            # Re-enable send button
            self.send_btn.setEnabled(True)
            self.send_btn.setText("Send")

            # Stop thinking animation
            self.thinking_display.stop_thinking()

            # Add thinking steps if available
            if 'thinking' in response:
                for step in response['thinking']:
                    step_type = "thinking"
                    if "error" in step.lower():
                        step_type = "error"
                    elif "success" in step.lower() or "complete" in step.lower():
                        step_type = "success"
                    elif "search" in step.lower():
                        step_type = "search"
                    elif "code" in step.lower():
                        step_type = "code"

                    self.thinking_display.add_thinking_step(step, step_type)

            # Add final response with streaming
            ai_message = response.get('response', 'Task completed')
            if self.current_tab_id and self.current_tab_id in self.chat_tabs:
                # Use streaming for agent responses
                self.add_ai_message(ai_message, "text", enable_streaming=True)

            # Handle tool results if available
            if 'tool_results' in response:
                for tool_result in response['tool_results']:
                    self._handle_tool_result(tool_result)

        except Exception as e:
            logger.error(f"Error handling agent response: {e}")
            self._on_error(f"Error processing agent response: {str(e)}")

    def _handle_tool_result(self, tool_result: Dict[str, Any]):
        """Handle tool execution result"""
        tool_name = tool_result.get('tool', 'unknown')
        success = tool_result.get('success', False)

        if not self.current_tab_id or self.current_tab_id not in self.chat_tabs:
            return

        current_tab = self.chat_tabs[self.current_tab_id]

        if tool_name == 'write_file' and success:
            file_path = tool_result.get('file_path', '')
            current_tab.add_message(f"✅ File created/updated: {file_path}", is_user=False)
        elif tool_name == 'read_file' and success:
            file_path = tool_result.get('file_path', '')
            current_tab.add_message(f"📖 File read: {file_path}", is_user=False)
        elif not success:
            error = tool_result.get('error', 'Unknown error')
            current_tab.add_message(f"❌ Tool error ({tool_name}): {error}", is_user=False)

    def _on_error(self, error_message: str):
        """Handle error"""
        # Re-enable send button
        self.send_btn.setEnabled(True)
        self.send_btn.setText("Send")

        # Stop thinking animation if active
        self.thinking_display.stop_thinking()

        # Add error message to current chat tab
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            current_tab = self.chat_tabs[self.current_tab_id]
            current_tab.add_message(f"❌ Error: {error_message}", is_user=False)

        logger.error(f"AI Sidebar error: {error_message}")

    # Public API methods
    def add_context_file(self, file_path: str):
        """Add file to context"""
        if file_path not in self.context_files:
            self.context_files.append(file_path)
            if self.current_tab_id and self.current_tab_id in self.chat_tabs:
                self.chat_tabs[self.current_tab_id].context_files.append(file_path)
            self._update_context_display()
            logger.info(f"Added file to context: {file_path}")

    def remove_context_file(self, file_path: str):
        """Remove file from context"""
        if file_path in self.context_files:
            self.context_files.remove(file_path)
            if self.current_tab_id and self.current_tab_id in self.chat_tabs:
                tab_context = self.chat_tabs[self.current_tab_id].context_files
                if file_path in tab_context:
                    tab_context.remove(file_path)
            self._update_context_display()
            logger.info(f"Removed file from context: {file_path}")

    def clear_context(self):
        """Clear all context files"""
        self.context_files.clear()
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            self.chat_tabs[self.current_tab_id].context_files.clear()
        self._update_context_display()
        logger.info("Context cleared")

    def set_server_url(self, url: str):
        """Update server URL"""
        self.api_client.set_server_url(url)
        self.server_label.setText(f"Connected to {url}")

    def get_current_chat_messages(self) -> List[Dict[str, Any]]:
        """Get messages from current chat tab"""
        if self.current_tab_id and self.current_tab_id in self.chat_tabs:
            return self.chat_tabs[self.current_tab_id].messages
        return []
