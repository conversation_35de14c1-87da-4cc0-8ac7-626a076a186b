"""
API Client for RCS Server Communication
"""

import asyncio
import json
import websockets
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
import requests
from loguru import logger
from PyQt6.QtCore import QObject, pyqtSignal, QThread
import uuid


@dataclass
class ChatMessage:
    """Chat message data structure"""
    message: str
    conversation_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


@dataclass
class AgentTask:
    """Agent task data structure"""
    task: str
    context: Optional[Dict[str, Any]] = None
    project_path: Optional[str] = None
    max_iterations: Optional[int] = None


class APIClient(QObject):
    """RCS Server API Client"""
    
    # Signals for UI updates
    connection_status_changed = pyqtSignal(bool)  # connected/disconnected
    chat_response_received = pyqtSignal(dict)     # chat response
    agent_response_received = pyqtSignal(dict)    # agent response
    file_operation_completed = pyqtSignal(dict)   # file operation result
    error_occurred = pyqtSignal(str)              # error message
    
    def __init__(self, base_url: str = "http://127.0.0.1:27027"):
        super().__init__()
        self.base_url = base_url.rstrip('/')
        self.ws_url = base_url.replace('http://', 'ws://').replace('https://', 'wss://')
        self.session = requests.Session()
        self.session.timeout = 30
        self.websocket = None
        self.client_id = str(uuid.uuid4())
        self.connected = False
        
    def set_server_url(self, url: str):
        """Update server URL"""
        self.base_url = url.rstrip('/')
        self.ws_url = url.replace('http://', 'ws://').replace('https://', 'wss://')
        
    async def connect_websocket(self):
        """Connect to WebSocket for real-time communication"""
        try:
            ws_endpoint = f"{self.ws_url}/ws/{self.client_id}"
            self.websocket = await websockets.connect(ws_endpoint)
            self.connected = True
            self.connection_status_changed.emit(True)
            logger.info(f"WebSocket connected to {ws_endpoint}")
            
            # Start listening for messages
            asyncio.create_task(self._listen_websocket())
            
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            self.connected = False
            self.connection_status_changed.emit(False)
            self.error_occurred.emit(f"WebSocket connection failed: {str(e)}")
    
    async def disconnect_websocket(self):
        """Disconnect WebSocket"""
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
            self.connected = False
            self.connection_status_changed.emit(False)
            logger.info("WebSocket disconnected")
    
    async def _listen_websocket(self):
        """Listen for WebSocket messages"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self._handle_websocket_message(data)
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
            self.connected = False
            self.connection_status_changed.emit(False)
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            self.error_occurred.emit(f"WebSocket error: {str(e)}")
    
    async def _handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages"""
        message_type = data.get('type', '')
        
        if message_type == 'chat_response':
            self.chat_response_received.emit(data)
        elif message_type == 'agent_response':
            self.agent_response_received.emit(data)
        elif message_type == 'file_operation':
            self.file_operation_completed.emit(data)
        elif message_type == 'error':
            self.error_occurred.emit(data.get('message', 'Unknown error'))
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    def health_check(self) -> Dict[str, Any]:
        """Check server health with enhanced error handling"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response.raise_for_status()
            result = response.json()

            # Handle both boolean and object responses
            if isinstance(result, bool):
                return {
                    "status": "healthy" if result else "unhealthy",
                    "healthy": result,
                    "server_reachable": True
                }
            elif isinstance(result, dict):
                # Ensure we have the required fields
                result["server_reachable"] = True
                if "healthy" not in result:
                    result["healthy"] = result.get("status") == "healthy"
                return result
            else:
                return {
                    "status": "healthy",
                    "healthy": True,
                    "server_reachable": True
                }

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection failed: {e}")
            return {
                "status": "connection_error",
                "message": "Cannot connect to server",
                "healthy": False,
                "server_reachable": False
            }
        except requests.exceptions.Timeout as e:
            logger.error(f"Request timeout: {e}")
            return {
                "status": "timeout",
                "message": "Server request timeout",
                "healthy": False,
                "server_reachable": False
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "error",
                "message": str(e),
                "healthy": False,
                "server_reachable": False
            }

    def get_server_info(self) -> Dict[str, Any]:
        """Get server information"""
        try:
            response = self.session.get(f"{self.base_url}/api", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Get server info failed: {e}")
            return {"error": True, "message": str(e)}
    
    def send_chat_message(self, message: ChatMessage) -> Dict[str, Any]:
        """Send chat message to server"""
        try:
            data = {
                "message": message.message,
                "conversation_id": message.conversation_id,
                "context": message.context
            }

            response = self.session.post(f"{self.base_url}/api/chat/message", json=data)
            response.raise_for_status()
            result = response.json()

            self.chat_response_received.emit(result)
            return result

        except Exception as e:
            logger.error(f"Chat message failed: {e}")
            error_msg = f"Chat failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            return {"error": error_msg}

    def send_chat_message_stream(self, message: ChatMessage, callback: Callable[[Dict[str, Any]], None]):
        """Send chat message with streaming response"""
        try:
            data = {
                "message": message.message,
                "conversation_id": message.conversation_id,
                "context": message.context
            }

            # Use streaming endpoint
            response = self.session.post(
                f"{self.base_url}/api/chat/message/stream",
                json=data,
                stream=True,
                timeout=60
            )
            response.raise_for_status()

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            chunk_data = json.loads(line_str[6:])  # Remove 'data: ' prefix
                            callback(chunk_data)

                            # Stop if done or error
                            if chunk_data.get('type') in ['done', 'error']:
                                break

                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse streaming chunk: {e}")
                            continue

            return {"success": True}

        except Exception as e:
            logger.error(f"Streaming chat failed: {e}")
            error_msg = f"Streaming chat failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            return {"error": error_msg}
    
    def send_agent_task(self, task: AgentTask) -> Dict[str, Any]:
        """Send agent task to server"""
        try:
            data = {
                "task": task.task,
                "context": task.context,
                "project_path": task.project_path,
                "max_iterations": task.max_iterations
            }
            
            response = self.session.post(f"{self.base_url}/api/agent/task", json=data)
            response.raise_for_status()
            result = response.json()
            
            self.agent_response_received.emit(result)
            return result
            
        except Exception as e:
            logger.error(f"Agent task failed: {e}")
            error_msg = f"Agent task failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            return {"error": error_msg}
    
    def read_file(self, file_path: str) -> Dict[str, Any]:
        """Read file from server"""
        try:
            params = {"path": file_path}
            response = self.session.get(f"{self.base_url}/api/files/read", params=params)
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Read file failed: {e}")
            return {"success": False, "error": str(e)}
    
    def write_file(self, file_path: str, content: str, create_dirs: bool = True) -> Dict[str, Any]:
        """Write file to server"""
        try:
            data = {
                "path": file_path,
                "content": content,
                "create_dirs": create_dirs
            }
            
            response = self.session.post(f"{self.base_url}/api/files/write", json=data)
            response.raise_for_status()
            result = response.json()
            
            self.file_operation_completed.emit(result)
            return result
            
        except Exception as e:
            logger.error(f"Write file failed: {e}")
            error_msg = f"Write file failed: {str(e)}"
            self.error_occurred.emit(error_msg)
            return {"success": False, "error": error_msg}
    
    def list_directory(self, directory_path: str, recursive: bool = False) -> Dict[str, Any]:
        """List directory contents"""
        try:
            params = {"path": directory_path, "recursive": recursive}
            response = self.session.get(f"{self.base_url}/api/files/list", params=params)
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"List directory failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_models(self) -> Dict[str, Any]:
        """Get available models with enhanced error handling"""
        try:
            response = self.session.get(f"{self.base_url}/api/models/available", timeout=15)
            response.raise_for_status()
            result = response.json()

            # Ensure we have the expected structure
            if isinstance(result, dict) and "models" in result:
                # Filter and enhance model data
                models = result["models"]
                enhanced_models = []

                for model in models:
                    if isinstance(model, dict):
                        # Ensure required fields exist
                        enhanced_model = {
                            "id": model.get("id", model.get("name", "unknown")),
                            "name": model.get("name", "Unknown Model"),
                            "type": model.get("type", "unknown"),
                            "description": model.get("description", ""),
                            "capabilities": model.get("capabilities", []),
                            "recommended": model.get("recommended", False)
                        }
                        enhanced_models.append(enhanced_model)

                return {
                    "success": True,
                    "models": enhanced_models,
                    "count": len(enhanced_models)
                }
            else:
                return {"success": False, "error": "Invalid response format", "models": []}

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection failed when getting models: {e}")
            return {"success": False, "error": "Cannot connect to server", "models": []}
        except requests.exceptions.Timeout as e:
            logger.error(f"Timeout when getting models: {e}")
            return {"success": False, "error": "Request timeout", "models": []}
        except Exception as e:
            logger.error(f"Get models failed: {e}")
            return {"success": False, "error": str(e), "models": []}

    def load_model(self, model_name: str) -> Dict[str, Any]:
        """Load a specific model"""
        try:
            # Use query parameters as expected by FastAPI
            params = {
                "model_name": model_name,
                "force_reload": True
            }
            response = self.session.post(f"{self.base_url}/api/models/load",
                                       params=params,
                                       timeout=60)
            response.raise_for_status()
            return response.json()

        except Exception as e:
            logger.error(f"Load model failed: {e}")
            return {"success": False, "error": str(e)}

    def reload_model(self, model_name: str = None) -> Dict[str, Any]:
        """Reload model with optional new model name"""
        try:
            data = {}
            if model_name:
                data["model_name"] = model_name

            response = self.session.post(f"{self.base_url}/api/models/reload",
                                       json=data, timeout=60)
            response.raise_for_status()
            return response.json()

        except Exception as e:
            logger.error(f"Reload model failed: {e}")
            return {"success": False, "error": str(e)}
    
    def web_search(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """Perform web search"""
        try:
            params = {"query": query, "num_results": num_results}
            response = self.session.post(f"{self.base_url}/api/chat/search", params=params)
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return {"success": False, "error": str(e)}


class WebSocketThread(QThread):
    """Thread for handling WebSocket connections"""
    
    def __init__(self, api_client: APIClient):
        super().__init__()
        self.api_client = api_client
        self.loop = None
    
    def run(self):
        """Run WebSocket event loop in separate thread"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self.api_client.connect_websocket())
            self.loop.run_forever()
        except Exception as e:
            logger.error(f"WebSocket thread error: {e}")
        finally:
            self.loop.close()
    
    def stop(self):
        """Stop WebSocket thread"""
        if self.loop and self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)
