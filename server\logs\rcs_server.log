2025-06-07 01:01:36 | INFO     | __main__:main:341 - Starting RCS CLI...
2025-06-07 01:01:36 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:01:36 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:03:01 | INFO     | app.services.model_manager:unload_model:263 - Model unloaded successfully
2025-06-07 01:03:01 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 01:03:01 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:03:01 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:03:01 | INFO     | app.services.model_manager:load_model:171 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:03:01 | WARNING  | app.services.model_manager:_load_transformers_model:210 - BitsAndBytes not available, loading without quantization
2025-06-07 01:03:01 | INFO     | app.services.model_manager:_load_transformers_model:213 - Loading tokenizer...
2025-06-07 01:03:03 | ERROR    | app.services.model_manager:_load_transformers_model:242 - Failed to load transformers model: DeepSeek-R1-0528-Qwen3-8B is not a local folder and is not a valid model identifier listed on 'https://huggingface.co/models'
If this is a private repository, make sure to pass a token having permission to this repo either by logging in with `huggingface-cli login` or by passing `token=<your_token>`
2025-06-07 01:03:03 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-07 01:03:03 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-07 01:03:03 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-07 01:03:03 | INFO     | app.services.ai_service:initialize:53 - AI Service initialization complete
2025-06-07 01:15:43 | INFO     | __main__:main:344 - Starting RCS CLI...
2025-06-07 01:15:43 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:15:43 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:15:43 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:15:43 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:15:43 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:17:34 | INFO     | __main__:main:348 - Starting Rilance Code Studio Server...
2025-06-07 01:17:34 | INFO     | __main__:main:354 - ======================================================================
2025-06-07 01:17:34 | INFO     | __main__:main:355 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 01:17:34 | INFO     | __main__:main:356 - ======================================================================
2025-06-07 01:17:34 | INFO     | __main__:main:357 - 📍 Primary IP: ************
2025-06-07 01:17:34 | INFO     | __main__:main:358 - 🔌 Port: 8000
2025-06-07 01:17:34 | INFO     | __main__:main:359 - 🌐 Host: 127.0.0.1
2025-06-07 01:17:34 | INFO     | __main__:main:360 - 
2025-06-07 01:17:34 | INFO     | __main__:main:361 - 📡 Available Network Addresses:
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • Hostname: RAIDEN-SILVER
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • IP: ************
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • IP: ***************
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • IP: ***************
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • IP: ::1
2025-06-07 01:17:34 | INFO     | __main__:main:363 -    • IP: 127.0.0.1
2025-06-07 01:17:34 | INFO     | __main__:main:364 - 
2025-06-07 01:17:34 | INFO     | __main__:main:365 - 📋 Important URLs:
2025-06-07 01:17:34 | INFO     | __main__:main:366 -    🏠 Main Page: http://************:8000/
2025-06-07 01:17:34 | INFO     | __main__:main:367 -    📖 Documentation: http://************:8000/static/index.html
2025-06-07 01:17:34 | INFO     | __main__:main:368 -    ❤️  Health Check: http://************:8000/health
2025-06-07 01:17:34 | INFO     | __main__:main:369 -    📚 API Docs: http://************:8000/api/docs
2025-06-07 01:17:34 | INFO     | __main__:main:370 -    🔌 WebSocket: ws://************:8000/ws/{client_id}
2025-06-07 01:17:34 | INFO     | __main__:main:371 - 
2025-06-07 01:17:34 | INFO     | __main__:main:374 - 🤖 AI Models Status:
2025-06-07 01:17:34 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:17:34 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:17:34 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:17:34 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:17:34 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:17:34 | INFO     | __main__:main:390 -    📦 Local Models (1 found):
2025-06-07 01:17:34 | INFO     | __main__:main:393 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 01:17:34 | INFO     | __main__:main:401 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 01:17:34 | INFO     | __main__:main:405 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 01:17:34 | INFO     | __main__:main:411 - 
2025-06-07 01:17:34 | INFO     | __main__:main:412 - ⚙️ Server Configuration:
2025-06-07 01:17:34 | INFO     | __main__:main:413 -    🏠 Host: 127.0.0.1
2025-06-07 01:17:34 | INFO     | __main__:main:414 -    🔌 Port: 8000
2025-06-07 01:17:34 | INFO     | __main__:main:415 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 01:17:34 | INFO     | __main__:main:416 -    📝 Log Level: DEBUG
2025-06-07 01:17:34 | INFO     | __main__:main:417 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 01:17:34 | INFO     | __main__:main:420 - 
2025-06-07 01:17:34 | INFO     | __main__:main:421 - 🎯 Quick Start Guide:
2025-06-07 01:17:34 | INFO     | __main__:main:422 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 01:17:34 | INFO     | __main__:main:423 -    2. 🖥️ Start RCS client application
2025-06-07 01:17:34 | INFO     | __main__:main:424 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 01:17:35 | INFO     | __main__:main:425 -    4. 💻 Begin coding with AI assistance!
2025-06-07 01:17:35 | INFO     | __main__:main:427 - 
2025-06-07 01:17:35 | INFO     | __main__:main:428 - 📋 Available CLI Commands:
2025-06-07 01:17:35 | INFO     | __main__:main:429 -    • python main.py --cli          - Start CLI mode
2025-06-07 01:17:35 | INFO     | __main__:main:430 -    • models list                   - List available models
2025-06-07 01:17:35 | INFO     | __main__:main:431 -    • models load <model_name>      - Load a specific model
2025-06-07 01:17:35 | INFO     | __main__:main:432 -    • health                        - Check server health
2025-06-07 01:17:35 | INFO     | __main__:main:434 - ======================================================================
2025-06-07 01:17:35 | INFO     | __main__:main:435 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 01:17:35 | INFO     | __main__:main:436 - ======================================================================
2025-06-07 01:20:12 | INFO     | __main__:main:344 - Starting RCS CLI...
2025-06-07 01:20:12 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:20:12 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:20:12 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:20:12 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:20:12 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:20:42 | INFO     | app.services.model_manager:unload_model:329 - Model unloaded successfully
2025-06-07 01:20:42 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:20:42 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:20:42 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:20:42 | INFO     | app.services.model_manager:load_model:227 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_load_transformers_model:258 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_load_transformers_model:259 - Loading from: Local directory
2025-06-07 01:20:42 | WARNING  | app.services.model_manager:_load_transformers_model:273 - BitsAndBytes not available, loading without quantization
2025-06-07 01:20:42 | INFO     | app.services.model_manager:_load_transformers_model:276 - Loading tokenizer...
2025-06-07 01:20:43 | INFO     | app.services.model_manager:_load_transformers_model:288 - Loading model...
2025-06-07 01:20:43 | ERROR    | app.services.model_manager:_load_transformers_model:308 - ❌ Failed to load transformers model: Using a `device_map`, `tp_plan`, `torch.device` context manager or setting `torch.set_default_device(device)` requires `accelerate`. You can install it with `pip install accelerate`
2025-06-07 01:20:43 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-07 01:20:43 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-07 01:20:43 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-07 01:20:43 | INFO     | app.services.ai_service:initialize:53 - AI Service initialization complete
2025-06-07 01:24:14 | INFO     | __main__:main:344 - Starting RCS CLI...
2025-06-07 01:24:14 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:24:14 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:24:14 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:24:14 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:24:14 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:24:51 | INFO     | app.services.model_manager:unload_model:357 - Model unloaded successfully
2025-06-07 01:24:51 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:24:51 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:24:51 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:24:51 | INFO     | app.services.model_manager:load_model:227 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_load_transformers_model:258 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_load_transformers_model:259 - Loading from: Local directory
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_load_transformers_model:267 - ✅ Accelerate available - using device mapping
2025-06-07 01:24:51 | WARNING  | app.services.model_manager:_load_transformers_model:284 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 01:24:51 | INFO     | app.services.model_manager:_load_transformers_model:287 - Loading tokenizer...
2025-06-07 01:24:52 | INFO     | app.services.model_manager:_load_transformers_model:313 - Loading model...
2025-06-07 01:26:44 | INFO     | app.services.model_manager:_load_transformers_model:324 - ✅ Model loaded successfully: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:26:44 | INFO     | app.services.model_manager:_load_transformers_model:325 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-07 01:26:44 | INFO     | app.services.model_manager:_load_transformers_model:331 - 🔋 GPU Memory - Allocated: 4.75GB, Reserved: 4.77GB
2025-06-07 01:33:10 | INFO     | __main__:main:344 - Starting RCS CLI...
2025-06-07 01:33:10 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:33:10 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:33:10 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:33:10 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:33:10 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:33:45 | INFO     | app.services.model_manager:unload_model:357 - Model unloaded successfully
2025-06-07 01:33:45 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:33:45 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:33:45 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:33:45 | INFO     | app.services.model_manager:load_model:227 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_load_transformers_model:258 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_load_transformers_model:259 - Loading from: Local directory
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_load_transformers_model:267 - ✅ Accelerate available - using device mapping
2025-06-07 01:33:45 | WARNING  | app.services.model_manager:_load_transformers_model:284 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 01:33:45 | INFO     | app.services.model_manager:_load_transformers_model:287 - Loading tokenizer...
2025-06-07 01:33:46 | INFO     | app.services.model_manager:_load_transformers_model:313 - Loading model...
2025-06-07 01:35:36 | INFO     | app.services.model_manager:_load_transformers_model:324 - ✅ Model loaded successfully: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 01:35:36 | INFO     | app.services.model_manager:_load_transformers_model:325 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-07 01:35:36 | INFO     | app.services.model_manager:_load_transformers_model:331 - 🔋 GPU Memory - Allocated: 4.75GB, Reserved: 4.77GB
2025-06-07 01:35:39 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-07 01:35:39 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-07 01:35:39 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-07 01:35:39 | INFO     | app.services.ai_service:initialize:53 - AI Service initialization complete
2025-06-07 01:37:25 | INFO     | app.services.model_manager:unload_model:345 - Unloading current model...
2025-06-07 01:37:25 | INFO     | app.services.model_manager:unload_model:357 - Model unloaded successfully
2025-06-07 01:42:01 | INFO     | __main__:main:363 - Starting Rilance Code Studio Server...
2025-06-07 01:42:01 | INFO     | __main__:main:369 - ======================================================================
2025-06-07 01:42:01 | INFO     | __main__:main:370 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 01:42:01 | INFO     | __main__:main:371 - ======================================================================
2025-06-07 01:42:01 | INFO     | __main__:main:372 - 📍 Primary IP: ************
2025-06-07 01:42:01 | INFO     | __main__:main:373 - 🔌 Port: 8000
2025-06-07 01:42:01 | INFO     | __main__:main:374 - 🌐 Host: 127.0.0.1
2025-06-07 01:42:01 | INFO     | __main__:main:375 - 
2025-06-07 01:42:01 | INFO     | __main__:main:376 - 📡 Available Network Addresses:
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • Hostname: RAIDEN-SILVER
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • IP: ************
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • IP: ::1
2025-06-07 01:42:01 | INFO     | __main__:main:378 -    • IP: 127.0.0.1
2025-06-07 01:42:01 | INFO     | __main__:main:379 - 
2025-06-07 01:42:01 | INFO     | __main__:main:380 - 📋 Important URLs:
2025-06-07 01:42:01 | INFO     | __main__:main:381 -    🏠 Main Page: http://************:8000/
2025-06-07 01:42:01 | INFO     | __main__:main:382 -    📖 Documentation: http://************:8000/static/index.html
2025-06-07 01:42:01 | INFO     | __main__:main:383 -    ❤️  Health Check: http://************:8000/health
2025-06-07 01:42:01 | INFO     | __main__:main:384 -    📚 API Docs: http://************:8000/api/docs
2025-06-07 01:42:01 | INFO     | __main__:main:385 -    🔌 WebSocket: ws://************:8000/ws/{client_id}
2025-06-07 01:42:01 | INFO     | __main__:main:386 - 
2025-06-07 01:42:01 | INFO     | __main__:main:389 - 🤖 AI Models Status:
2025-06-07 01:42:01 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:42:01 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:42:01 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:42:01 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:42:01 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:42:01 | INFO     | __main__:main:405 -    📦 Local Models (1 found):
2025-06-07 01:42:01 | INFO     | __main__:main:408 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 01:42:01 | INFO     | __main__:main:416 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 01:42:01 | INFO     | __main__:main:420 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 01:42:01 | INFO     | __main__:main:426 - 
2025-06-07 01:42:01 | INFO     | __main__:main:427 - ⚙️ Server Configuration:
2025-06-07 01:42:01 | INFO     | __main__:main:428 -    🏠 Host: 127.0.0.1
2025-06-07 01:42:01 | INFO     | __main__:main:429 -    🔌 Port: 8000
2025-06-07 01:42:01 | INFO     | __main__:main:430 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 01:42:01 | INFO     | __main__:main:431 -    📝 Log Level: DEBUG
2025-06-07 01:42:01 | INFO     | __main__:main:432 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 01:42:01 | INFO     | __main__:main:435 - 
2025-06-07 01:42:01 | INFO     | __main__:main:436 - 🎯 Quick Start Guide:
2025-06-07 01:42:01 | INFO     | __main__:main:437 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 01:42:01 | INFO     | __main__:main:438 -    2. 🖥️ Start RCS client application
2025-06-07 01:42:01 | INFO     | __main__:main:439 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 01:42:01 | INFO     | __main__:main:440 -    4. 💻 Begin coding with AI assistance!
2025-06-07 01:42:01 | INFO     | __main__:main:442 - 
2025-06-07 01:42:01 | INFO     | __main__:main:443 - 📋 Available CLI Commands:
2025-06-07 01:42:01 | INFO     | __main__:main:444 -    • python main.py --cli          - Start CLI mode
2025-06-07 01:42:01 | INFO     | __main__:main:445 -    • models list                   - List available models
2025-06-07 01:42:01 | INFO     | __main__:main:446 -    • models load <model_name>      - Load a specific model
2025-06-07 01:42:01 | INFO     | __main__:main:447 -    • health                        - Check server health
2025-06-07 01:42:01 | INFO     | __main__:main:449 - ======================================================================
2025-06-07 01:42:01 | INFO     | __main__:main:450 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 01:42:01 | INFO     | __main__:main:451 - ======================================================================
2025-06-07 01:47:36 | INFO     | __main__:main:363 - Starting Rilance Code Studio Server...
2025-06-07 01:47:36 | INFO     | __main__:main:369 - ======================================================================
2025-06-07 01:47:36 | INFO     | __main__:main:370 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 01:47:36 | INFO     | __main__:main:371 - ======================================================================
2025-06-07 01:47:36 | INFO     | __main__:main:372 - 📍 Primary IP: ************
2025-06-07 01:47:36 | INFO     | __main__:main:373 - 🔌 Port: 8000
2025-06-07 01:47:36 | INFO     | __main__:main:374 - 🌐 Host: 127.0.0.1
2025-06-07 01:47:36 | INFO     | __main__:main:375 - 
2025-06-07 01:47:36 | INFO     | __main__:main:376 - 📡 Available Network Addresses:
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • Hostname: RAIDEN-SILVER
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • IP: ************
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • IP: ::1
2025-06-07 01:47:36 | INFO     | __main__:main:378 -    • IP: 127.0.0.1
2025-06-07 01:47:36 | INFO     | __main__:main:379 - 
2025-06-07 01:47:36 | INFO     | __main__:main:380 - 📋 Important URLs:
2025-06-07 01:47:36 | INFO     | __main__:main:381 -    🏠 Main Page: http://************:8000/
2025-06-07 01:47:36 | INFO     | __main__:main:382 -    📖 Documentation: http://************:8000/static/index.html
2025-06-07 01:47:36 | INFO     | __main__:main:383 -    ❤️  Health Check: http://************:8000/health
2025-06-07 01:47:36 | INFO     | __main__:main:384 -    📚 API Docs: http://************:8000/api/docs
2025-06-07 01:47:36 | INFO     | __main__:main:385 -    🔌 WebSocket: ws://************:8000/ws/{client_id}
2025-06-07 01:47:36 | INFO     | __main__:main:386 - 
2025-06-07 01:47:36 | INFO     | __main__:main:389 - 🤖 AI Models Status:
2025-06-07 01:47:36 | INFO     | app.services.model_manager:_get_local_models:46 - 🔍 Scanning for local models in: data\models
2025-06-07 01:47:36 | INFO     | app.services.model_manager:_get_local_models:76 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 01:47:36 | INFO     | app.services.model_manager:_get_local_models:100 - 📊 Total local models found: 1
2025-06-07 01:47:36 | INFO     | app.services.model_manager:refresh_models:39 - Refreshed models: 3 categories
2025-06-07 01:47:36 | INFO     | app.services.model_manager:initialize:30 - Model manager initialized
2025-06-07 01:47:36 | INFO     | __main__:main:405 -    📦 Local Models (1 found):
2025-06-07 01:47:36 | INFO     | __main__:main:408 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 01:47:36 | INFO     | __main__:main:416 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 01:47:36 | INFO     | __main__:main:420 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 01:47:36 | INFO     | __main__:main:426 - 
2025-06-07 01:47:36 | INFO     | __main__:main:427 - ⚙️ Server Configuration:
2025-06-07 01:47:36 | INFO     | __main__:main:428 -    🏠 Host: 127.0.0.1
2025-06-07 01:47:36 | INFO     | __main__:main:429 -    🔌 Port: 8000
2025-06-07 01:47:36 | INFO     | __main__:main:430 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 01:47:36 | INFO     | __main__:main:431 -    📝 Log Level: DEBUG
2025-06-07 01:47:36 | INFO     | __main__:main:432 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 01:47:36 | INFO     | __main__:main:435 - 
2025-06-07 01:47:36 | INFO     | __main__:main:436 - 🎯 Quick Start Guide:
2025-06-07 01:47:36 | INFO     | __main__:main:437 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 01:47:36 | INFO     | __main__:main:438 -    2. 🖥️ Start RCS client application
2025-06-07 01:47:36 | INFO     | __main__:main:439 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 01:47:36 | INFO     | __main__:main:440 -    4. 💻 Begin coding with AI assistance!
2025-06-07 01:47:36 | INFO     | __main__:main:442 - 
2025-06-07 01:47:36 | INFO     | __main__:main:443 - 📋 Available CLI Commands:
2025-06-07 01:47:36 | INFO     | __main__:main:444 -    • python main.py --cli          - Start CLI mode
2025-06-07 01:47:36 | INFO     | __main__:main:445 -    • models list                   - List available models
2025-06-07 01:47:36 | INFO     | __main__:main:446 -    • models load <model_name>      - Load a specific model
2025-06-07 01:47:36 | INFO     | __main__:main:447 -    • health                        - Check server health
2025-06-07 01:47:36 | INFO     | __main__:main:449 - ======================================================================
2025-06-07 01:47:36 | INFO     | __main__:main:450 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 01:47:36 | INFO     | __main__:main:451 - ======================================================================
2025-06-07 02:37:47 | INFO     | __main__:main:363 - Starting Rilance Code Studio Server...
2025-06-07 02:37:47 | INFO     | __main__:main:369 - ======================================================================
2025-06-07 02:37:47 | INFO     | __main__:main:370 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 02:37:47 | INFO     | __main__:main:371 - ======================================================================
2025-06-07 02:37:47 | INFO     | __main__:main:372 - 📍 Primary IP: ************
2025-06-07 02:37:47 | INFO     | __main__:main:373 - 🔌 Port: 8000
2025-06-07 02:37:47 | INFO     | __main__:main:374 - 🌐 Host: 127.0.0.1
2025-06-07 02:37:47 | INFO     | __main__:main:375 - 
2025-06-07 02:37:47 | INFO     | __main__:main:376 - 📡 Available Network Addresses:
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • Hostname: RAIDEN-SILVER
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • IP: ************
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • IP: ::1
2025-06-07 02:37:47 | INFO     | __main__:main:378 -    • IP: 127.0.0.1
2025-06-07 02:37:47 | INFO     | __main__:main:379 - 
2025-06-07 02:37:47 | INFO     | __main__:main:380 - 📋 Important URLs:
2025-06-07 02:37:47 | INFO     | __main__:main:381 -    🏠 Main Page: http://************:8000/
2025-06-07 02:37:47 | INFO     | __main__:main:382 -    📖 Documentation: http://************:8000/static/index.html
2025-06-07 02:37:47 | INFO     | __main__:main:383 -    ❤️  Health Check: http://************:8000/health
2025-06-07 02:37:47 | INFO     | __main__:main:384 -    📚 API Docs: http://************:8000/api/docs
2025-06-07 02:37:47 | INFO     | __main__:main:385 -    🔌 WebSocket: ws://************:8000/ws/{client_id}
2025-06-07 02:37:47 | INFO     | __main__:main:386 - 
2025-06-07 02:37:47 | INFO     | __main__:main:389 - 🤖 AI Models Status:
2025-06-07 02:37:47 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 02:37:47 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 02:37:47 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 02:37:47 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 02:37:47 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 02:37:47 | INFO     | __main__:main:405 -    📦 Local Models (1 found):
2025-06-07 02:37:47 | INFO     | __main__:main:408 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 02:37:47 | INFO     | __main__:main:416 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 02:37:47 | INFO     | __main__:main:420 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 02:37:47 | INFO     | __main__:main:426 - 
2025-06-07 02:37:47 | INFO     | __main__:main:427 - ⚙️ Server Configuration:
2025-06-07 02:37:47 | INFO     | __main__:main:428 -    🏠 Host: 127.0.0.1
2025-06-07 02:37:47 | INFO     | __main__:main:429 -    🔌 Port: 8000
2025-06-07 02:37:47 | INFO     | __main__:main:430 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 02:37:47 | INFO     | __main__:main:431 -    📝 Log Level: DEBUG
2025-06-07 02:37:47 | INFO     | __main__:main:432 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 02:37:47 | INFO     | __main__:main:435 - 
2025-06-07 02:37:47 | INFO     | __main__:main:436 - 🎯 Quick Start Guide:
2025-06-07 02:37:47 | INFO     | __main__:main:437 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 02:37:47 | INFO     | __main__:main:438 -    2. 🖥️ Start RCS client application
2025-06-07 02:37:47 | INFO     | __main__:main:439 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 02:37:47 | INFO     | __main__:main:440 -    4. 💻 Begin coding with AI assistance!
2025-06-07 02:37:47 | INFO     | __main__:main:442 - 
2025-06-07 02:37:47 | INFO     | __main__:main:443 - 📋 Available CLI Commands:
2025-06-07 02:37:47 | INFO     | __main__:main:444 -    • python main.py --cli          - Start CLI mode
2025-06-07 02:37:47 | INFO     | __main__:main:445 -    • models list                   - List available models
2025-06-07 02:37:47 | INFO     | __main__:main:446 -    • models load <model_name>      - Load a specific model
2025-06-07 02:37:47 | INFO     | __main__:main:447 -    • health                        - Check server health
2025-06-07 02:37:47 | INFO     | __main__:main:449 - ======================================================================
2025-06-07 02:37:47 | INFO     | __main__:main:450 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 02:37:47 | INFO     | __main__:main:451 - ======================================================================
2025-06-07 02:38:45 | INFO     | __main__:main:363 - Starting Rilance Code Studio Server...
2025-06-07 02:38:45 | INFO     | __main__:main:369 - ======================================================================
2025-06-07 02:38:45 | INFO     | __main__:main:370 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 02:38:45 | INFO     | __main__:main:371 - ======================================================================
2025-06-07 02:38:45 | INFO     | __main__:main:372 - 📍 Primary IP: ************
2025-06-07 02:38:45 | INFO     | __main__:main:373 - 🔌 Port: 27027
2025-06-07 02:38:45 | INFO     | __main__:main:374 - 🌐 Host: 127.0.0.1
2025-06-07 02:38:45 | INFO     | __main__:main:375 - 
2025-06-07 02:38:45 | INFO     | __main__:main:376 - 📡 Available Network Addresses:
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • Hostname: RAIDEN-SILVER
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • IP: ************
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • IP: ***************
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • IP: ::1
2025-06-07 02:38:45 | INFO     | __main__:main:378 -    • IP: 127.0.0.1
2025-06-07 02:38:45 | INFO     | __main__:main:379 - 
2025-06-07 02:38:45 | INFO     | __main__:main:380 - 📋 Important URLs:
2025-06-07 02:38:45 | INFO     | __main__:main:381 -    🏠 Main Page: http://************:27027/
2025-06-07 02:38:45 | INFO     | __main__:main:382 -    📖 Documentation: http://************:27027/static/index.html
2025-06-07 02:38:45 | INFO     | __main__:main:383 -    ❤️  Health Check: http://************:27027/health
2025-06-07 02:38:45 | INFO     | __main__:main:384 -    📚 API Docs: http://************:27027/api/docs
2025-06-07 02:38:45 | INFO     | __main__:main:385 -    🔌 WebSocket: ws://************:27027/ws/{client_id}
2025-06-07 02:38:45 | INFO     | __main__:main:386 - 
2025-06-07 02:38:45 | INFO     | __main__:main:389 - 🤖 AI Models Status:
2025-06-07 02:38:45 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 02:38:45 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 02:38:45 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 02:38:45 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 02:38:45 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 02:38:45 | INFO     | __main__:main:405 -    📦 Local Models (1 found):
2025-06-07 02:38:45 | INFO     | __main__:main:408 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 02:38:45 | INFO     | __main__:main:416 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 02:38:45 | INFO     | __main__:main:420 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 02:38:45 | INFO     | __main__:main:426 - 
2025-06-07 02:38:45 | INFO     | __main__:main:427 - ⚙️ Server Configuration:
2025-06-07 02:38:45 | INFO     | __main__:main:428 -    🏠 Host: 127.0.0.1
2025-06-07 02:38:45 | INFO     | __main__:main:429 -    🔌 Port: 27027
2025-06-07 02:38:45 | INFO     | __main__:main:430 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 02:38:45 | INFO     | __main__:main:431 -    📝 Log Level: DEBUG
2025-06-07 02:38:45 | INFO     | __main__:main:432 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 02:38:45 | INFO     | __main__:main:435 - 
2025-06-07 02:38:45 | INFO     | __main__:main:436 - 🎯 Quick Start Guide:
2025-06-07 02:38:45 | INFO     | __main__:main:437 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 02:38:45 | INFO     | __main__:main:438 -    2. 🖥️ Start RCS client application
2025-06-07 02:38:45 | INFO     | __main__:main:439 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 02:38:45 | INFO     | __main__:main:440 -    4. 💻 Begin coding with AI assistance!
2025-06-07 02:38:45 | INFO     | __main__:main:442 - 
2025-06-07 02:38:45 | INFO     | __main__:main:443 - 📋 Available CLI Commands:
2025-06-07 02:38:45 | INFO     | __main__:main:444 -    • python main.py --cli          - Start CLI mode
2025-06-07 02:38:45 | INFO     | __main__:main:445 -    • models list                   - List available models
2025-06-07 02:38:45 | INFO     | __main__:main:446 -    • models load <model_name>      - Load a specific model
2025-06-07 02:38:45 | INFO     | __main__:main:447 -    • health                        - Check server health
2025-06-07 02:38:45 | INFO     | __main__:main:449 - ======================================================================
2025-06-07 02:38:45 | INFO     | __main__:main:450 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 02:38:45 | INFO     | __main__:main:451 - ======================================================================
2025-06-07 02:39:23 | INFO     | __main__:main:359 - Starting RCS CLI...
2025-06-07 02:39:23 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 02:39:23 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 02:39:23 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 02:39:23 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 02:39:23 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 02:43:02 | INFO     | __main__:main:359 - Starting RCS CLI...
2025-06-07 02:43:02 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 02:43:02 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 02:43:02 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 02:43:02 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 02:43:02 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 02:43:40 | INFO     | app.services.model_manager:download_model:441 - 🔄 Starting download of microsoft/DialoGPT-small from https://huggingface.co/microsoft/DialoGPT-small
2025-06-07 02:43:40 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: Cloning into 'data\models\microsoft\DialoGPT-small'...
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Enumerating objects: 62, done.
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  11% (1/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  22% (2/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  33% (3/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  44% (4/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  55% (5/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  66% (6/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  77% (7/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects:  88% (8/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects: 100% (9/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Counting objects: 100% (9/9), done.
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  11% (1/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  22% (2/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  33% (3/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  44% (4/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  55% (5/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  66% (6/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  77% (7/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects:  88% (8/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects: 100% (9/9)
2025-06-07 02:43:42 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Compressing objects: 100% (9/9), done.
2025-06-07 02:43:43 | INFO     | app.services.model_manager:download_model:469 - 📥 Git: remote: Total 62 (delta 3), reused 0 (delta 0), pack-reused 53 (from 1)
2025-06-07 03:11:17 | INFO     | __main__:main:359 - Starting RCS CLI...
2025-06-07 03:11:17 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 03:11:17 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 03:11:17 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 03:11:17 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 03:11:17 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 03:13:29 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-07 03:13:29 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 03:13:29 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 03:13:29 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 03:13:29 | INFO     | app.services.model_manager:load_model:229 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-07 03:13:29 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 03:13:29 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-07 03:13:30 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-07 04:11:17 | INFO     | __main__:main:387 - Starting Rilance Code Studio Server...
2025-06-07 04:11:17 | INFO     | __main__:main:393 - ======================================================================
2025-06-07 04:11:17 | INFO     | __main__:main:394 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 04:11:17 | INFO     | __main__:main:395 - ======================================================================
2025-06-07 04:11:17 | INFO     | __main__:main:396 - 📍 Primary IP: ************
2025-06-07 04:11:17 | INFO     | __main__:main:397 - 🔌 Port: 8000
2025-06-07 04:11:17 | INFO     | __main__:main:398 - 🌐 Host: 127.0.0.1
2025-06-07 04:11:17 | INFO     | __main__:main:399 - 
2025-06-07 04:11:17 | INFO     | __main__:main:400 - 📡 Available Network Addresses:
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • Hostname: RAIDEN-SILVER
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • IP: ************
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • IP: ***************
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • IP: ***************
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • IP: ::1
2025-06-07 04:11:17 | INFO     | __main__:main:402 -    • IP: 127.0.0.1
2025-06-07 04:11:17 | INFO     | __main__:main:403 - 
2025-06-07 04:11:17 | INFO     | __main__:main:404 - 📋 Important URLs:
2025-06-07 04:11:17 | INFO     | __main__:main:405 -    🏠 Main Page: http://************:8000/
2025-06-07 04:11:17 | INFO     | __main__:main:406 -    📖 Documentation: http://************:8000/static/index.html
2025-06-07 04:11:17 | INFO     | __main__:main:407 -    ❤️  Health Check: http://************:8000/health
2025-06-07 04:11:17 | INFO     | __main__:main:408 -    📚 API Docs: http://************:8000/api/docs
2025-06-07 04:11:17 | INFO     | __main__:main:409 -    🔌 WebSocket: ws://************:8000/ws/{client_id}
2025-06-07 04:11:17 | INFO     | __main__:main:410 - 
2025-06-07 04:11:17 | INFO     | __main__:main:413 - 🤖 AI Models Status:
2025-06-07 04:11:17 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:11:17 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:11:17 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:11:17 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:11:17 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:11:17 | INFO     | __main__:main:429 -    📦 Local Models (1 found):
2025-06-07 04:11:17 | INFO     | __main__:main:432 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 04:11:17 | INFO     | __main__:main:440 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 04:11:17 | INFO     | __main__:main:444 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 04:11:17 | INFO     | __main__:main:450 - 
2025-06-07 04:11:17 | INFO     | __main__:main:451 - ⚙️ Server Configuration:
2025-06-07 04:11:17 | INFO     | __main__:main:452 -    🏠 Host: 127.0.0.1
2025-06-07 04:11:17 | INFO     | __main__:main:453 -    🔌 Port: 8000
2025-06-07 04:11:17 | INFO     | __main__:main:454 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 04:11:17 | INFO     | __main__:main:455 -    📝 Log Level: DEBUG
2025-06-07 04:11:17 | INFO     | __main__:main:456 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 04:11:17 | INFO     | __main__:main:459 - 
2025-06-07 04:11:17 | INFO     | __main__:main:460 - 🎯 Quick Start Guide:
2025-06-07 04:11:17 | INFO     | __main__:main:461 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 04:11:17 | INFO     | __main__:main:462 -    2. 🖥️ Start RCS client application
2025-06-07 04:11:17 | INFO     | __main__:main:463 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 04:11:17 | INFO     | __main__:main:464 -    4. 💻 Begin coding with AI assistance!
2025-06-07 04:11:17 | INFO     | __main__:main:466 - 
2025-06-07 04:11:17 | INFO     | __main__:main:467 - 📋 Available CLI Commands:
2025-06-07 04:11:17 | INFO     | __main__:main:468 -    • python main.py --cli          - Start CLI mode
2025-06-07 04:11:17 | INFO     | __main__:main:469 -    • models list                   - List available models
2025-06-07 04:11:17 | INFO     | __main__:main:470 -    • models load <model_name>      - Load a specific model
2025-06-07 04:11:17 | INFO     | __main__:main:471 -    • health                        - Check server health
2025-06-07 04:11:17 | INFO     | __main__:main:473 - ======================================================================
2025-06-07 04:11:17 | INFO     | __main__:main:474 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 04:11:17 | INFO     | __main__:main:475 - ======================================================================
2025-06-07 04:17:17 | INFO     | __main__:main:387 - Starting Rilance Code Studio Server...
2025-06-07 04:17:17 | INFO     | __main__:main:393 - ======================================================================
2025-06-07 04:17:17 | INFO     | __main__:main:394 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 04:17:17 | INFO     | __main__:main:395 - ======================================================================
2025-06-07 04:17:17 | INFO     | __main__:main:396 - 📍 Primary IP: ************
2025-06-07 04:17:17 | INFO     | __main__:main:397 - 🔌 Port: 27027
2025-06-07 04:17:17 | INFO     | __main__:main:398 - 🌐 Host: 127.0.0.1
2025-06-07 04:17:17 | INFO     | __main__:main:399 - 
2025-06-07 04:17:17 | INFO     | __main__:main:400 - 📡 Available Network Addresses:
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • Hostname: RAIDEN-SILVER
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • IP: ************
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • IP: ***************
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • IP: ***************
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • IP: ::1
2025-06-07 04:17:17 | INFO     | __main__:main:402 -    • IP: 127.0.0.1
2025-06-07 04:17:17 | INFO     | __main__:main:403 - 
2025-06-07 04:17:17 | INFO     | __main__:main:404 - 📋 Important URLs:
2025-06-07 04:17:17 | INFO     | __main__:main:405 -    🏠 Main Page: http://************:27027/
2025-06-07 04:17:17 | INFO     | __main__:main:406 -    📖 Documentation: http://************:27027/static/index.html
2025-06-07 04:17:17 | INFO     | __main__:main:407 -    ❤️  Health Check: http://************:27027/health
2025-06-07 04:17:17 | INFO     | __main__:main:408 -    📚 API Docs: http://************:27027/api/docs
2025-06-07 04:17:17 | INFO     | __main__:main:409 -    🔌 WebSocket: ws://************:27027/ws/{client_id}
2025-06-07 04:17:17 | INFO     | __main__:main:410 - 
2025-06-07 04:17:17 | INFO     | __main__:main:413 - 🤖 AI Models Status:
2025-06-07 04:17:17 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:17:17 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:17:17 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:17:17 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:17:17 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:17:17 | INFO     | __main__:main:429 -    📦 Local Models (1 found):
2025-06-07 04:17:17 | INFO     | __main__:main:432 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 04:17:17 | INFO     | __main__:main:440 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 04:17:17 | INFO     | __main__:main:444 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 04:17:17 | INFO     | __main__:main:450 - 
2025-06-07 04:17:17 | INFO     | __main__:main:451 - ⚙️ Server Configuration:
2025-06-07 04:17:17 | INFO     | __main__:main:452 -    🏠 Host: 127.0.0.1
2025-06-07 04:17:17 | INFO     | __main__:main:453 -    🔌 Port: 27027
2025-06-07 04:17:17 | INFO     | __main__:main:454 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 04:17:17 | INFO     | __main__:main:455 -    📝 Log Level: DEBUG
2025-06-07 04:17:17 | INFO     | __main__:main:456 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 04:17:17 | INFO     | __main__:main:459 - 
2025-06-07 04:17:17 | INFO     | __main__:main:460 - 🎯 Quick Start Guide:
2025-06-07 04:17:17 | INFO     | __main__:main:461 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 04:17:17 | INFO     | __main__:main:462 -    2. 🖥️ Start RCS client application
2025-06-07 04:17:17 | INFO     | __main__:main:463 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 04:17:17 | INFO     | __main__:main:464 -    4. 💻 Begin coding with AI assistance!
2025-06-07 04:17:17 | INFO     | __main__:main:466 - 
2025-06-07 04:17:17 | INFO     | __main__:main:467 - 📋 Available CLI Commands:
2025-06-07 04:17:17 | INFO     | __main__:main:468 -    • python main.py --cli          - Start CLI mode
2025-06-07 04:17:17 | INFO     | __main__:main:469 -    • models list                   - List available models
2025-06-07 04:17:17 | INFO     | __main__:main:470 -    • models load <model_name>      - Load a specific model
2025-06-07 04:17:17 | INFO     | __main__:main:471 -    • health                        - Check server health
2025-06-07 04:17:17 | INFO     | __main__:main:473 - ======================================================================
2025-06-07 04:17:17 | INFO     | __main__:main:474 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 04:17:17 | INFO     | __main__:main:475 - ======================================================================
2025-06-07 04:18:13 | INFO     | __main__:main:383 - Starting RCS CLI...
2025-06-07 04:18:13 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:18:13 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:18:13 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:18:13 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:18:13 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:20:38 | INFO     | __main__:main:383 - Starting RCS CLI...
2025-06-07 04:20:38 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:20:38 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:20:38 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:20:38 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:20:38 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:24:04 | INFO     | __main__:main:393 - Starting RCS CLI...
2025-06-07 04:24:04 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:24:04 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:24:04 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:24:04 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:24:04 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:24:36 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-07 04:24:36 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:24:36 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:24:36 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:24:36 | INFO     | app.services.model_manager:load_model:229 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-07 04:24:36 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 04:24:36 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-07 04:24:37 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-07 04:26:02 | INFO     | __main__:main:393 - Starting RCS CLI...
2025-06-07 04:26:02 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:26:02 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:26:02 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:26:02 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:26:02 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:28:17 | INFO     | __main__:main:393 - Starting RCS CLI...
2025-06-07 04:28:17 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:28:17 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:28:17 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:28:17 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:28:17 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:40:15 | INFO     | __main__:main:397 - Starting Rilance Code Studio Server...
2025-06-07 04:40:15 | INFO     | __main__:main:403 - ======================================================================
2025-06-07 04:40:15 | INFO     | __main__:main:404 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 04:40:15 | INFO     | __main__:main:405 - ======================================================================
2025-06-07 04:40:15 | INFO     | __main__:main:406 - 📍 Primary IP: ************
2025-06-07 04:40:15 | INFO     | __main__:main:407 - 🔌 Port: 27027
2025-06-07 04:40:15 | INFO     | __main__:main:408 - 🌐 Host: 127.0.0.1
2025-06-07 04:40:15 | INFO     | __main__:main:409 - 
2025-06-07 04:40:15 | INFO     | __main__:main:410 - 📡 Available Network Addresses:
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • Hostname: RAIDEN-SILVER
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • IP: ************
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • IP: ***************
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • IP: ***************
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • IP: ::1
2025-06-07 04:40:15 | INFO     | __main__:main:412 -    • IP: 127.0.0.1
2025-06-07 04:40:15 | INFO     | __main__:main:413 - 
2025-06-07 04:40:15 | INFO     | __main__:main:414 - 📋 Important URLs:
2025-06-07 04:40:15 | INFO     | __main__:main:415 -    🏠 Main Page: http://************:27027/
2025-06-07 04:40:15 | INFO     | __main__:main:416 -    📖 Documentation: http://************:27027/static/index.html
2025-06-07 04:40:15 | INFO     | __main__:main:417 -    ❤️  Health Check: http://************:27027/health
2025-06-07 04:40:15 | INFO     | __main__:main:418 -    📚 API Docs: http://************:27027/api/docs
2025-06-07 04:40:15 | INFO     | __main__:main:419 -    🔌 WebSocket: ws://************:27027/ws/{client_id}
2025-06-07 04:40:15 | INFO     | __main__:main:420 - 
2025-06-07 04:40:15 | INFO     | __main__:main:423 - 🤖 AI Models Status:
2025-06-07 04:40:15 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:40:15 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:40:15 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:40:15 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:40:15 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:40:15 | INFO     | __main__:main:439 -    📦 Local Models (1 found):
2025-06-07 04:40:15 | INFO     | __main__:main:442 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 04:40:15 | INFO     | __main__:main:450 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 04:40:15 | INFO     | __main__:main:454 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 04:40:15 | INFO     | __main__:main:460 - 
2025-06-07 04:40:15 | INFO     | __main__:main:461 - ⚙️ Server Configuration:
2025-06-07 04:40:15 | INFO     | __main__:main:462 -    🏠 Host: 127.0.0.1
2025-06-07 04:40:15 | INFO     | __main__:main:463 -    🔌 Port: 27027
2025-06-07 04:40:15 | INFO     | __main__:main:464 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 04:40:15 | INFO     | __main__:main:465 -    📝 Log Level: DEBUG
2025-06-07 04:40:15 | INFO     | __main__:main:466 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 04:40:15 | INFO     | __main__:main:469 - 
2025-06-07 04:40:15 | INFO     | __main__:main:470 - 🎯 Quick Start Guide:
2025-06-07 04:40:15 | INFO     | __main__:main:471 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 04:40:15 | INFO     | __main__:main:472 -    2. 🖥️ Start RCS client application
2025-06-07 04:40:15 | INFO     | __main__:main:473 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 04:40:15 | INFO     | __main__:main:474 -    4. 💻 Begin coding with AI assistance!
2025-06-07 04:40:15 | INFO     | __main__:main:476 - 
2025-06-07 04:40:15 | INFO     | __main__:main:477 - 📋 Available CLI Commands:
2025-06-07 04:40:15 | INFO     | __main__:main:478 -    • python main.py --cli          - Start CLI mode
2025-06-07 04:40:15 | INFO     | __main__:main:479 -    • models list                   - List available models
2025-06-07 04:40:15 | INFO     | __main__:main:480 -    • models load <model_name>      - Load a specific model
2025-06-07 04:40:15 | INFO     | __main__:main:481 -    • health                        - Check server health
2025-06-07 04:40:15 | INFO     | __main__:main:483 - ======================================================================
2025-06-07 04:40:15 | INFO     | __main__:main:484 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 04:40:15 | INFO     | __main__:main:485 - ======================================================================
2025-06-07 04:41:18 | INFO     | __main__:main:393 - Starting RCS CLI...
2025-06-07 04:41:18 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:41:18 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:41:18 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:41:18 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:41:18 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:43:18 | INFO     | __main__:main:397 - Starting Rilance Code Studio Server...
2025-06-07 04:43:18 | INFO     | __main__:main:403 - ======================================================================
2025-06-07 04:43:18 | INFO     | __main__:main:404 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-07 04:43:18 | INFO     | __main__:main:405 - ======================================================================
2025-06-07 04:43:18 | INFO     | __main__:main:406 - 📍 Primary IP: ************
2025-06-07 04:43:18 | INFO     | __main__:main:407 - 🔌 Port: 27027
2025-06-07 04:43:18 | INFO     | __main__:main:408 - 🌐 Host: 127.0.0.1
2025-06-07 04:43:18 | INFO     | __main__:main:409 - 
2025-06-07 04:43:18 | INFO     | __main__:main:410 - 📡 Available Network Addresses:
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • Hostname: RAIDEN-SILVER
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • IP: ************
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • IP: ***************
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • IP: ***************
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • IP: ::1
2025-06-07 04:43:18 | INFO     | __main__:main:412 -    • IP: 127.0.0.1
2025-06-07 04:43:18 | INFO     | __main__:main:413 - 
2025-06-07 04:43:18 | INFO     | __main__:main:414 - 📋 Important URLs:
2025-06-07 04:43:18 | INFO     | __main__:main:415 -    🏠 Main Page: http://************:27027/
2025-06-07 04:43:18 | INFO     | __main__:main:416 -    📖 Documentation: http://************:27027/static/index.html
2025-06-07 04:43:18 | INFO     | __main__:main:417 -    ❤️  Health Check: http://************:27027/health
2025-06-07 04:43:18 | INFO     | __main__:main:418 -    📚 API Docs: http://************:27027/api/docs
2025-06-07 04:43:18 | INFO     | __main__:main:419 -    🔌 WebSocket: ws://************:27027/ws/{client_id}
2025-06-07 04:43:18 | INFO     | __main__:main:420 - 
2025-06-07 04:43:18 | INFO     | __main__:main:423 - 🤖 AI Models Status:
2025-06-07 04:43:18 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:43:18 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:43:18 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:43:18 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:43:18 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 04:43:18 | INFO     | __main__:main:439 -    📦 Local Models (1 found):
2025-06-07 04:43:18 | INFO     | __main__:main:442 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-07 04:43:18 | INFO     | __main__:main:450 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-07 04:43:18 | INFO     | __main__:main:454 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-07 04:43:18 | INFO     | __main__:main:460 - 
2025-06-07 04:43:18 | INFO     | __main__:main:461 - ⚙️ Server Configuration:
2025-06-07 04:43:18 | INFO     | __main__:main:462 -    🏠 Host: 127.0.0.1
2025-06-07 04:43:18 | INFO     | __main__:main:463 -    🔌 Port: 27027
2025-06-07 04:43:18 | INFO     | __main__:main:464 -    🐛 Debug Mode: ✅ Enabled
2025-06-07 04:43:18 | INFO     | __main__:main:465 -    📝 Log Level: DEBUG
2025-06-07 04:43:18 | INFO     | __main__:main:466 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-07 04:43:18 | INFO     | __main__:main:469 - 
2025-06-07 04:43:18 | INFO     | __main__:main:470 - 🎯 Quick Start Guide:
2025-06-07 04:43:18 | INFO     | __main__:main:471 -    1. 📖 Open documentation: http://{local_ip}:{args.port}/static/index.html
2025-06-07 04:43:18 | INFO     | __main__:main:472 -    2. 🖥️ Start RCS client application
2025-06-07 04:43:18 | INFO     | __main__:main:473 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-07 04:43:18 | INFO     | __main__:main:474 -    4. 💻 Begin coding with AI assistance!
2025-06-07 04:43:18 | INFO     | __main__:main:476 - 
2025-06-07 04:43:18 | INFO     | __main__:main:477 - 📋 Available CLI Commands:
2025-06-07 04:43:18 | INFO     | __main__:main:478 -    • python main.py --cli          - Start CLI mode
2025-06-07 04:43:18 | INFO     | __main__:main:479 -    • models list                   - List available models
2025-06-07 04:43:18 | INFO     | __main__:main:480 -    • models load <model_name>      - Load a specific model
2025-06-07 04:43:18 | INFO     | __main__:main:481 -    • health                        - Check server health
2025-06-07 04:43:18 | INFO     | __main__:main:483 - ======================================================================
2025-06-07 04:43:18 | INFO     | __main__:main:484 - 🎉 Server initialization complete! Ready to serve requests.
2025-06-07 04:43:18 | INFO     | __main__:main:485 - ======================================================================
2025-06-07 04:43:41 | INFO     | __main__:main:393 - Starting RCS CLI...
2025-06-07 04:43:41 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 04:43:41 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 04:43:41 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 04:43:41 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 04:43:41 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:34:33 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-07 05:34:33 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-07 05:34:33 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:34:33 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:34:33 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:34:33 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:34:33 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:34:33 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-07 05:38:00 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-07 05:38:00 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-07 05:38:00 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:38:00 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:38:00 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:38:00 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:38:00 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:38:00 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-07 05:38:42 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-07 05:38:42 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-07 05:38:42 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:38:42 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:38:42 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:38:42 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:38:42 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:38:42 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-07 05:39:29 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-07 05:39:29 | INFO     | cli:list_models:243 - 📊 Found 1 local models, 8 HuggingFace models
2025-06-07 05:40:21 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:40:21 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-07 05:40:21 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-07 05:40:21 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-07 05:40:21 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:40:21 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:40:21 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:40:21 | INFO     | app.services.model_manager:load_model:229 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-07 05:40:21 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-07 05:40:21 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-07 05:42:32 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-07 05:42:32 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-07 05:42:32 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:42:32 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:42:32 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:42:32 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:42:32 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:42:32 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-07 05:42:55 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-07 05:42:55 | INFO     | cli:list_models:243 - 📊 Found 1 local models, 8 HuggingFace models
2025-06-07 05:45:02 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:45:02 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-07 05:45:02 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-07 05:45:02 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-07 05:45:02 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 05:45:02 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 05:45:02 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 05:45:02 | INFO     | app.services.model_manager:load_model:229 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-07 05:45:02 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 05:45:02 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-07 05:45:03 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-07 23:09:30 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-07 23:09:30 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-07 23:09:30 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 23:09:30 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 23:09:30 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 23:09:30 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 23:09:30 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 23:09:30 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-07 23:12:13 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 23:12:13 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-07 23:12:13 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-07 23:12:13 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-07 23:12:13 | INFO     | app.services.ai_service:initialize:39 - Initializing AI Service...
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-07 23:12:13 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-07 23:12:13 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-07 23:12:13 | INFO     | app.services.model_manager:load_model:229 - Loading model: DeepSeek-R1-0528-Qwen3-8B
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\DeepSeek-R1-0528-Qwen3-8B
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-07 23:12:13 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-07 23:12:13 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-07 23:12:14 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-08 06:23:11 | INFO     | __main__:main:393 - 🚀 Starting RCS CLI...
2025-06-08 06:23:11 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-08 06:23:11 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-08 06:23:11 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-08 06:23:11 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-08 06:23:11 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-08 06:23:11 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-08 06:23:11 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-08 08:49:43 | INFO     | __main__:main:398 - 🚀 Starting RCS CLI...
2025-06-08 08:49:43 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-08 08:49:43 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-08 08:49:43 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-08 08:49:43 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 1
2025-06-08 08:49:43 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-08 08:49:43 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-08 08:49:43 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-08 08:49:51 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-08 08:49:51 | INFO     | cli:list_models:243 - 📊 Found 1 local models, 8 HuggingFace models
2025-06-08 09:12:18 | INFO     | __main__:main:398 - 🚀 Starting RCS CLI...
2025-06-08 09:12:18 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-08 09:12:18 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-08 09:12:18 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-08 09:12:18 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-08 09:12:18 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-08 09:12:18 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-08 09:12:18 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-08 09:12:18 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-08 09:13:37 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-08 09:13:37 | INFO     | cli:list_models:243 - 📊 Found 2 local models, 8 HuggingFace models
2025-06-08 09:14:23 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: Qwen3-0.6B
2025-06-08 09:14:23 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-08 09:14:23 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-08 09:14:23 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-08 09:14:23 | INFO     | app.services.ai_service:initialize:40 - Initializing AI Service...
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-08 09:14:23 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-08 09:14:23 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-08 09:14:23 | INFO     | app.services.model_manager:load_model:229 - Loading model: Qwen3-0.6B
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\Qwen3-0.6B
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-08 09:14:23 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-08 09:14:23 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-08 09:14:24 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-08 09:14:26 | INFO     | app.services.model_manager:_load_transformers_model:326 - ✅ Model loaded successfully: Qwen3-0.6B
2025-06-08 09:14:26 | INFO     | app.services.model_manager:_load_transformers_model:327 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-08 09:14:26 | INFO     | app.services.model_manager:_load_transformers_model:333 - 🔋 GPU Memory - Allocated: 1.11GB, Reserved: 1.42GB
2025-06-08 09:14:26 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-08 09:14:26 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-08 09:14:26 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-08 09:14:26 | INFO     | app.services.ai_service:initialize:54 - AI Service initialization complete
2025-06-09 05:59:25 | INFO     | __main__:main:398 - 🚀 Starting RCS CLI...
2025-06-09 05:59:25 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 05:59:25 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 05:59:26 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 05:59:26 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 05:59:26 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 05:59:26 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 05:59:26 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 05:59:26 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 05:59:38 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-09 05:59:38 | INFO     | cli:list_models:243 - 📊 Found 2 local models, 8 HuggingFace models
2025-06-09 05:59:51 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: Qwen3-0.6B
2025-06-09 05:59:51 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-09 05:59:51 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-09 05:59:51 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-09 05:59:51 | INFO     | app.services.ai_service:initialize:40 - Initializing AI Service...
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 05:59:51 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 05:59:51 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 05:59:51 | INFO     | app.services.model_manager:load_model:229 - Loading model: Qwen3-0.6B
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\Qwen3-0.6B
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-09 05:59:51 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-09 05:59:51 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-09 05:59:59 | INFO     | app.services.model_manager:_load_transformers_model:326 - ✅ Model loaded successfully: Qwen3-0.6B
2025-06-09 05:59:59 | INFO     | app.services.model_manager:_load_transformers_model:327 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-09 05:59:59 | INFO     | app.services.model_manager:_load_transformers_model:333 - 🔋 GPU Memory - Allocated: 1.11GB, Reserved: 1.42GB
2025-06-09 06:00:00 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-09 06:00:00 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-09 06:00:00 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-09 06:00:00 | INFO     | app.services.ai_service:initialize:54 - AI Service initialization complete
2025-06-09 06:19:34 | INFO     | __main__:main:431 - Starting Rilance Code Studio Server...
2025-06-09 06:19:34 | INFO     | __main__:main:438 - ================================================================================
2025-06-09 06:19:34 | INFO     | __main__:main:439 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-09 06:19:34 | INFO     | __main__:main:440 - ================================================================================
2025-06-09 06:19:34 | INFO     | __main__:main:441 - 📍 Server Host: 127.0.0.1
2025-06-09 06:19:34 | INFO     | __main__:main:442 - 🔌 Server Port: 27027
2025-06-09 06:19:34 | INFO     | __main__:main:443 - 🌐 Primary IP: ************
2025-06-09 06:19:34 | INFO     | __main__:main:444 - 
2025-06-09 06:19:34 | INFO     | __main__:main:447 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-09 06:19:34 | INFO     | __main__:main:448 -    ┌─────────────────────────────────────────────────────────┐
2025-06-09 06:19:34 | INFO     | __main__:main:449 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-09 06:19:34 | INFO     | __main__:main:450 -    │                                                         │
2025-06-09 06:19:34 | INFO     | __main__:main:453 -    │  1. Server IP: ************:27027 (PRIMARY)      │
2025-06-09 06:19:34 | INFO     | __main__:main:453 -    │  2. Server IP: 127.0.0.1:27027 (LOCALHOST)    │
2025-06-09 06:19:34 | INFO     | __main__:main:454 -    │                                                         │
2025-06-09 06:19:34 | INFO     | __main__:main:455 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-09 06:19:34 | INFO     | __main__:main:456 -    │     Enter in format: IP:PORT (e.g., *************:27027) │
2025-06-09 06:19:34 | INFO     | __main__:main:457 -    └─────────────────────────────────────────────────────────┘
2025-06-09 06:19:34 | INFO     | __main__:main:458 - 
2025-06-09 06:19:34 | INFO     | __main__:main:460 - 📡 All Available Network Addresses:
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • Hostname: RAIDEN-SILVER
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • IP: ************
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • IP: ***************
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • IP: ***************
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • IP: ::1
2025-06-09 06:19:34 | INFO     | __main__:main:462 -    • IP: 127.0.0.1
2025-06-09 06:19:34 | INFO     | __main__:main:463 - 
2025-06-09 06:19:34 | INFO     | __main__:main:464 - 📋 Web Interface URLs:
2025-06-09 06:19:34 | INFO     | __main__:main:465 -    🏠 Main Page: http://************:27027/
2025-06-09 06:19:34 | INFO     | __main__:main:466 -    📖 Documentation: http://************:27027/static/index.html
2025-06-09 06:19:34 | INFO     | __main__:main:467 -    ❤️  Health Check: http://************:27027/health
2025-06-09 06:19:34 | INFO     | __main__:main:468 -    📚 API Docs: http://************:27027/api/docs
2025-06-09 06:19:34 | INFO     | __main__:main:469 - 
2025-06-09 06:19:34 | INFO     | __main__:main:471 - 🔌 API Endpoints for Client:
2025-06-09 06:19:34 | INFO     | __main__:main:472 -    📡 HTTP API: http://************:27027/api/
2025-06-09 06:19:34 | INFO     | __main__:main:473 -    🌐 WebSocket: ws://************:27027/ws/{client_id}
2025-06-09 06:19:34 | INFO     | __main__:main:474 -    🔍 Health Check: http://************:27027/health
2025-06-09 06:19:34 | INFO     | __main__:main:475 - 
2025-06-09 06:19:34 | INFO     | __main__:main:478 - 🤖 AI Models Status:
2025-06-09 06:19:34 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:19:34 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:19:34 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:19:34 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:19:34 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:19:34 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:19:34 | INFO     | __main__:main:494 -    📦 Local Models (2 found):
2025-06-09 06:19:34 | INFO     | __main__:main:497 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-09 06:19:34 | INFO     | __main__:main:497 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-09 06:19:34 | INFO     | __main__:main:505 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-09 06:19:34 | INFO     | __main__:main:509 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-09 06:19:34 | INFO     | __main__:main:515 - 
2025-06-09 06:19:34 | INFO     | __main__:main:516 - ⚙️ Server Configuration:
2025-06-09 06:19:34 | INFO     | __main__:main:517 -    🏠 Host: 127.0.0.1
2025-06-09 06:19:34 | INFO     | __main__:main:518 -    🔌 Port: 27027
2025-06-09 06:19:34 | INFO     | __main__:main:519 -    🐛 Debug Mode: ✅ Enabled
2025-06-09 06:19:34 | INFO     | __main__:main:520 -    📝 Log Level: DEBUG
2025-06-09 06:19:34 | INFO     | __main__:main:521 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-09 06:19:34 | INFO     | __main__:main:524 - 
2025-06-09 06:19:34 | INFO     | __main__:main:525 - 🎯 Quick Start Guide:
2025-06-09 06:19:34 | INFO     | __main__:main:526 -    1. 🖥️ Start RCS IDE Client Application
2025-06-09 06:19:34 | INFO     | __main__:main:527 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-09 06:19:34 | INFO     | __main__:main:528 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-09 06:19:34 | INFO     | __main__:main:529 -    4. 💻 Begin coding with AI assistance!
2025-06-09 06:19:34 | INFO     | __main__:main:530 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-09 06:19:34 | INFO     | __main__:main:532 - 
2025-06-09 06:19:34 | INFO     | __main__:main:533 - 📋 Available CLI Commands:
2025-06-09 06:19:34 | INFO     | __main__:main:534 -    • python main.py --cli          - Start CLI mode
2025-06-09 06:19:34 | INFO     | __main__:main:535 -    • models list                   - List available models
2025-06-09 06:19:34 | INFO     | __main__:main:536 -    • models load <model_name>      - Load a specific model
2025-06-09 06:19:34 | INFO     | __main__:main:537 -    • health                        - Check server health
2025-06-09 06:19:34 | INFO     | __main__:main:539 - ================================================================================
2025-06-09 06:19:34 | INFO     | __main__:main:540 - 🎉 SERVER READY! Clients can now connect using the IPs shown above.
2025-06-09 06:19:34 | INFO     | __main__:main:541 - ================================================================================
2025-06-09 06:20:34 | INFO     | __main__:main:427 - 🚀 Starting RCS CLI...
2025-06-09 06:20:34 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 06:20:34 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:20:34 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:20:34 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:20:34 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:20:34 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:20:34 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:20:34 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 06:21:42 | INFO     | __main__:main:427 - 🚀 Starting RCS CLI...
2025-06-09 06:21:42 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 06:21:42 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:21:42 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:21:42 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:21:42 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:21:42 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:21:42 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:21:42 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 06:22:40 | INFO     | cli:list_models:241 - 🔍 Scanning for available models...
2025-06-09 06:22:40 | INFO     | cli:list_models:243 - 📊 Found 2 local models, 8 HuggingFace models
2025-06-09 06:22:49 | INFO     | cli:load_model:297 - 📝 Updating model configuration to: Qwen3-0.6B
2025-06-09 06:22:49 | INFO     | cli:load_model:301 - 🧹 Cleaning up current model...
2025-06-09 06:22:49 | INFO     | app.services.model_manager:unload_model:359 - Model unloaded successfully
2025-06-09 06:22:49 | INFO     | cli:load_model:305 - 🚀 Initializing new model...
2025-06-09 06:22:49 | INFO     | app.services.ai_service:initialize:40 - Initializing AI Service...
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:22:49 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:22:49 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:22:49 | INFO     | app.services.model_manager:load_model:229 - Loading model: Qwen3-0.6B
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_load_transformers_model:260 - Model path: data\models\Qwen3-0.6B
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_load_transformers_model:261 - Loading from: Local directory
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_load_transformers_model:269 - ✅ Accelerate available - using device mapping
2025-06-09 06:22:49 | WARNING  | app.services.model_manager:_load_transformers_model:286 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_load_transformers_model:289 - Loading tokenizer...
2025-06-09 06:22:49 | INFO     | app.services.model_manager:_load_transformers_model:315 - Loading model...
2025-06-09 06:22:51 | INFO     | app.services.model_manager:_load_transformers_model:326 - ✅ Model loaded successfully: Qwen3-0.6B
2025-06-09 06:22:51 | INFO     | app.services.model_manager:_load_transformers_model:327 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-09 06:22:51 | INFO     | app.services.model_manager:_load_transformers_model:333 - 🔋 GPU Memory - Allocated: 1.11GB, Reserved: 1.42GB
2025-06-09 06:22:51 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-09 06:22:51 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-09 06:22:51 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-09 06:22:51 | INFO     | app.services.ai_service:initialize:54 - AI Service initialization complete
2025-06-09 06:34:26 | INFO     | __main__:main:427 - 🚀 Starting RCS CLI...
2025-06-09 06:34:26 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 06:34:26 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:34:26 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:34:26 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:34:26 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:34:26 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:34:26 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:34:26 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 06:52:55 | INFO     | __main__:main:531 - 🚀 Starting RCS CLI...
2025-06-09 06:52:55 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 06:52:55 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 06:52:55 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 06:52:55 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 06:52:55 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 06:52:55 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 06:52:55 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 06:52:55 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 08:00:45 | INFO     | __main__:main:643 - Starting Rilance Code Studio Server...
2025-06-09 08:00:45 | INFO     | __main__:main:661 - ================================================================================
2025-06-09 08:00:45 | INFO     | __main__:main:662 - 🚀 RILANCE CODE STUDIO SERVER
2025-06-09 08:00:45 | INFO     | __main__:main:663 - ================================================================================
2025-06-09 08:00:45 | INFO     | __main__:main:664 - 📍 Server Host: ************
2025-06-09 08:00:45 | INFO     | __main__:main:665 - 🔌 Server Port: 27027
2025-06-09 08:00:45 | INFO     | __main__:main:666 - 🌐 Primary IP: ************
2025-06-09 08:00:45 | INFO     | __main__:main:667 - 
2025-06-09 08:00:45 | INFO     | __main__:main:670 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-09 08:00:45 | INFO     | __main__:main:671 -    ┌─────────────────────────────────────────────────────────┐
2025-06-09 08:00:45 | INFO     | __main__:main:672 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-09 08:00:45 | INFO     | __main__:main:673 -    │                                                         │
2025-06-09 08:00:45 | INFO     | __main__:main:676 -    │  1. Server IP: ************:27027 (PRIMARY)      │
2025-06-09 08:00:45 | INFO     | __main__:main:677 -    │                                                         │
2025-06-09 08:00:45 | INFO     | __main__:main:678 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-09 08:00:45 | INFO     | __main__:main:679 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-09 08:00:45 | INFO     | __main__:main:680 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-09 08:00:45 | INFO     | __main__:main:681 -    └─────────────────────────────────────────────────────────┘
2025-06-09 08:00:45 | INFO     | __main__:main:682 - 
2025-06-09 08:00:45 | INFO     | __main__:main:684 - 📡 All Available Network Addresses:
2025-06-09 08:00:45 | INFO     | __main__:main:686 -    • IP: ************
2025-06-09 08:00:45 | INFO     | __main__:main:687 - 
2025-06-09 08:00:45 | INFO     | __main__:main:688 - 📋 Web Interface URLs:
2025-06-09 08:00:45 | INFO     | __main__:main:689 -    🏠 Main Page: http://************:27027/
2025-06-09 08:00:45 | INFO     | __main__:main:690 -    📖 Documentation: http://************:27027/static/index.html
2025-06-09 08:00:45 | INFO     | __main__:main:691 -    ❤️  Health Check: http://************:27027/health
2025-06-09 08:00:45 | INFO     | __main__:main:692 -    📚 API Docs: http://************:27027/api/docs
2025-06-09 08:00:45 | INFO     | __main__:main:693 - 
2025-06-09 08:00:45 | INFO     | __main__:main:695 - 🔌 API Endpoints for Client:
2025-06-09 08:00:45 | INFO     | __main__:main:696 -    📡 HTTP API: http://************:27027/api/
2025-06-09 08:00:45 | INFO     | __main__:main:697 -    🌐 WebSocket: ws://************:27027/ws/{client_id}
2025-06-09 08:00:45 | INFO     | __main__:main:698 -    🔍 Health Check: http://************:27027/health
2025-06-09 08:00:45 | INFO     | __main__:main:699 - 
2025-06-09 08:00:45 | INFO     | __main__:main:702 - 🤖 AI Models Status:
2025-06-09 08:00:45 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 08:00:45 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 08:00:45 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 08:00:45 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 08:00:45 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 08:00:45 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 08:00:45 | INFO     | __main__:main:718 -    📦 Local Models (2 found):
2025-06-09 08:00:45 | INFO     | __main__:main:721 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-09 08:00:45 | INFO     | __main__:main:721 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-09 08:00:45 | INFO     | __main__:main:729 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-09 08:00:45 | INFO     | __main__:main:733 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-09 08:00:45 | INFO     | __main__:main:739 - 
2025-06-09 08:00:45 | INFO     | __main__:main:740 - ⚙️ Server Configuration:
2025-06-09 08:00:45 | INFO     | __main__:main:741 -    🏠 Host: ************
2025-06-09 08:00:45 | INFO     | __main__:main:742 -    🔌 Port: 27027
2025-06-09 08:00:45 | INFO     | __main__:main:743 -    🐛 Debug Mode: ✅ Enabled
2025-06-09 08:00:45 | INFO     | __main__:main:744 -    📝 Log Level: DEBUG
2025-06-09 08:00:45 | INFO     | __main__:main:745 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-09 08:00:45 | INFO     | __main__:main:748 - 
2025-06-09 08:00:45 | INFO     | __main__:main:749 - 🎯 Quick Start Guide:
2025-06-09 08:00:45 | INFO     | __main__:main:750 -    1. 🖥️ Start RCS IDE Client Application
2025-06-09 08:00:45 | INFO     | __main__:main:751 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-09 08:00:45 | INFO     | __main__:main:752 -    3. 🤖 Load a model using CLI: python main.py --cli
2025-06-09 08:00:45 | INFO     | __main__:main:753 -    4. 💻 Begin coding with AI assistance!
2025-06-09 08:00:45 | INFO     | __main__:main:754 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-09 08:00:45 | INFO     | __main__:main:756 - 
2025-06-09 08:00:45 | INFO     | __main__:main:757 - 📋 Available CLI Commands:
2025-06-09 08:00:45 | INFO     | __main__:main:758 -    • python main.py --cli          - Start CLI mode
2025-06-09 08:00:45 | INFO     | __main__:main:759 -    • models list                   - List available models
2025-06-09 08:00:45 | INFO     | __main__:main:760 -    • models load <model_name>      - Load a specific model
2025-06-09 08:00:45 | INFO     | __main__:main:761 -    • health                        - Check server health
2025-06-09 08:00:45 | INFO     | __main__:main:763 - ================================================================================
2025-06-09 08:00:45 | INFO     | __main__:main:764 - 🎉 SERVER READY! Clients can now connect using the IPs shown above.
2025-06-09 08:00:45 | INFO     | __main__:main:765 - ================================================================================
2025-06-09 08:03:03 | INFO     | __main__:main:777 - 🚀 Starting RCS CLI...
2025-06-09 08:03:03 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-09 08:03:03 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 08:03:03 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 08:03:03 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 08:03:03 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 08:03:03 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 08:03:03 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 08:03:03 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-09 08:45:37 | INFO     | __main__:main:731 - Starting Rilance Code Studio Server with Integrated CLI...
2025-06-09 08:45:37 | INFO     | __main__:main:748 - ================================================================================
2025-06-09 08:45:37 | INFO     | __main__:main:749 - 🚀 RILANCE CODE STUDIO UNIFIED SERVER
2025-06-09 08:45:37 | INFO     | __main__:main:750 - ================================================================================
2025-06-09 08:45:37 | INFO     | __main__:main:751 - 📍 Server Host: ************
2025-06-09 08:45:37 | INFO     | __main__:main:752 - 🔌 Server Port: 27027
2025-06-09 08:45:37 | INFO     | __main__:main:753 - 🌐 Primary IP: ************
2025-06-09 08:45:37 | INFO     | __main__:main:754 - 🎮 CLI Interface: ✅ Integrated via API
2025-06-09 08:45:37 | INFO     | __main__:main:755 - 
2025-06-09 08:45:37 | INFO     | __main__:main:758 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-09 08:45:37 | INFO     | __main__:main:759 -    ┌─────────────────────────────────────────────────────────┐
2025-06-09 08:45:37 | INFO     | __main__:main:760 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-09 08:45:37 | INFO     | __main__:main:761 -    │                                                         │
2025-06-09 08:45:37 | INFO     | __main__:main:764 -    │  1. Server IP: ************:27027 (PRIMARY)      │
2025-06-09 08:45:37 | INFO     | __main__:main:765 -    │                                                         │
2025-06-09 08:45:37 | INFO     | __main__:main:766 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-09 08:45:37 | INFO     | __main__:main:767 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-09 08:45:37 | INFO     | __main__:main:768 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-09 08:45:37 | INFO     | __main__:main:769 -    └─────────────────────────────────────────────────────────┘
2025-06-09 08:45:37 | INFO     | __main__:main:770 - 
2025-06-09 08:45:37 | INFO     | __main__:main:772 - 📡 All Available Network Addresses:
2025-06-09 08:45:37 | INFO     | __main__:main:774 -    • IP: ************
2025-06-09 08:45:37 | INFO     | __main__:main:775 - 
2025-06-09 08:45:37 | INFO     | __main__:main:776 - 📋 Web Interface URLs:
2025-06-09 08:45:37 | INFO     | __main__:main:777 -    🏠 Main Page: http://************:27027/
2025-06-09 08:45:37 | INFO     | __main__:main:778 -    📖 Documentation: http://************:27027/static/index.html
2025-06-09 08:45:37 | INFO     | __main__:main:779 -    ❤️  Health Check: http://************:27027/health
2025-06-09 08:45:37 | INFO     | __main__:main:780 -    📚 API Docs: http://************:27027/api/docs
2025-06-09 08:45:37 | INFO     | __main__:main:781 - 
2025-06-09 08:45:37 | INFO     | __main__:main:783 - 🔌 API Endpoints for Client:
2025-06-09 08:45:37 | INFO     | __main__:main:784 -    📡 HTTP API: http://************:27027/api/
2025-06-09 08:45:37 | INFO     | __main__:main:785 -    🌐 WebSocket: ws://************:27027/ws/{client_id}
2025-06-09 08:45:37 | INFO     | __main__:main:786 -    🔍 Health Check: http://************:27027/health
2025-06-09 08:45:37 | INFO     | __main__:main:787 - 
2025-06-09 08:45:37 | INFO     | __main__:main:790 - 🤖 AI Models Status:
2025-06-09 08:45:37 | INFO     | app.services.model_manager:_get_local_models:48 - 🔍 Scanning for local models in: data\models
2025-06-09 08:45:37 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-09 08:45:37 | INFO     | app.services.model_manager:_get_local_models:78 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-09 08:45:37 | INFO     | app.services.model_manager:_get_local_models:102 - 📊 Total local models found: 2
2025-06-09 08:45:37 | INFO     | app.services.model_manager:refresh_models:41 - Refreshed models: 3 categories
2025-06-09 08:45:37 | INFO     | app.services.model_manager:initialize:32 - Model manager initialized
2025-06-09 08:45:37 | INFO     | __main__:main:806 -    📦 Local Models (2 found):
2025-06-09 08:45:37 | INFO     | __main__:main:809 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-09 08:45:37 | INFO     | __main__:main:809 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-09 08:45:37 | INFO     | __main__:main:817 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-09 08:45:37 | INFO     | __main__:main:821 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-09 08:45:37 | INFO     | __main__:main:827 - 
2025-06-09 08:45:37 | INFO     | __main__:main:828 - ⚙️ Server Configuration:
2025-06-09 08:45:37 | INFO     | __main__:main:829 -    🏠 Host: ************
2025-06-09 08:45:37 | INFO     | __main__:main:830 -    🔌 Port: 27027
2025-06-09 08:45:37 | INFO     | __main__:main:831 -    🐛 Debug Mode: ✅ Enabled
2025-06-09 08:45:37 | INFO     | __main__:main:832 -    📝 Log Level: DEBUG
2025-06-09 08:45:37 | INFO     | __main__:main:833 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-09 08:45:37 | INFO     | __main__:main:836 - 
2025-06-09 08:45:37 | INFO     | __main__:main:837 - 🎮 Integrated CLI Commands (via API):
2025-06-09 08:45:37 | INFO     | __main__:main:838 -    • POST /api/cli                 - Execute CLI commands
2025-06-09 08:45:37 | INFO     | __main__:main:839 -    • GET /api/cli/help             - Get CLI help
2025-06-09 08:45:37 | INFO     | __main__:main:840 -    • Available commands: help, models, config, test, health, system
2025-06-09 08:45:37 | INFO     | __main__:main:841 -    • Example: POST /api/cli {'command': 'models list'}
2025-06-09 08:45:37 | INFO     | __main__:main:842 -    • Example: POST /api/cli {'command': 'test chat Hello'}
2025-06-09 08:45:37 | INFO     | __main__:main:845 - 
2025-06-09 08:45:37 | INFO     | __main__:main:846 - 🎯 Quick Start Guide:
2025-06-09 08:45:37 | INFO     | __main__:main:847 -    1. 🖥️ Start RCS IDE Client Application
2025-06-09 08:45:37 | INFO     | __main__:main:848 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-09 08:45:37 | INFO     | __main__:main:849 -    3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}
2025-06-09 08:45:37 | INFO     | __main__:main:850 -    4. 💻 Begin coding with AI assistance!
2025-06-09 08:45:37 | INFO     | __main__:main:851 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-09 08:45:37 | INFO     | __main__:main:853 - ================================================================================
2025-06-09 08:45:37 | INFO     | __main__:main:854 - 🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.
2025-06-09 08:45:37 | INFO     | __main__:main:855 - ================================================================================
2025-06-10 06:18:25 | INFO     | __main__:main:836 - Starting Rilance Code Studio Server with Integrated CLI...
2025-06-10 06:18:25 | INFO     | __main__:main:853 - ================================================================================
2025-06-10 06:18:25 | INFO     | __main__:main:854 - 🚀 RILANCE CODE STUDIO UNIFIED SERVER
2025-06-10 06:18:25 | INFO     | __main__:main:855 - ================================================================================
2025-06-10 06:18:25 | INFO     | __main__:main:856 - 📍 Server Host: ************
2025-06-10 06:18:25 | INFO     | __main__:main:857 - 🔌 Server Port: 27027
2025-06-10 06:18:25 | INFO     | __main__:main:858 - 🌐 Primary IP: ************
2025-06-10 06:18:25 | INFO     | __main__:main:859 - 🎮 CLI Interface: ✅ Integrated via API
2025-06-10 06:18:25 | INFO     | __main__:main:860 - 
2025-06-10 06:18:25 | INFO     | __main__:main:863 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-10 06:18:25 | INFO     | __main__:main:864 -    ┌─────────────────────────────────────────────────────────┐
2025-06-10 06:18:25 | INFO     | __main__:main:865 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-10 06:18:25 | INFO     | __main__:main:866 -    │                                                         │
2025-06-10 06:18:25 | INFO     | __main__:main:869 -    │  1. Server IP: ************:27027 (PRIMARY)      │
2025-06-10 06:18:25 | INFO     | __main__:main:870 -    │                                                         │
2025-06-10 06:18:25 | INFO     | __main__:main:871 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-10 06:18:25 | INFO     | __main__:main:872 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-10 06:18:25 | INFO     | __main__:main:873 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-10 06:18:25 | INFO     | __main__:main:874 -    └─────────────────────────────────────────────────────────┘
2025-06-10 06:18:25 | INFO     | __main__:main:875 - 
2025-06-10 06:18:25 | INFO     | __main__:main:877 - 📡 All Available Network Addresses:
2025-06-10 06:18:25 | INFO     | __main__:main:879 -    • IP: ************
2025-06-10 06:18:25 | INFO     | __main__:main:880 - 
2025-06-10 06:18:25 | INFO     | __main__:main:881 - 📋 Web Interface URLs:
2025-06-10 06:18:25 | INFO     | __main__:main:882 -    🏠 Main Page: http://************:27027/
2025-06-10 06:18:25 | INFO     | __main__:main:883 -    📖 Documentation: http://************:27027/static/index.html
2025-06-10 06:18:25 | INFO     | __main__:main:884 -    ❤️  Health Check: http://************:27027/health
2025-06-10 06:18:25 | INFO     | __main__:main:885 -    📚 API Docs: http://************:27027/api/docs
2025-06-10 06:18:25 | INFO     | __main__:main:886 - 
2025-06-10 06:18:25 | INFO     | __main__:main:888 - 🔌 API Endpoints for Client:
2025-06-10 06:18:25 | INFO     | __main__:main:889 -    📡 HTTP API: http://************:27027/api/
2025-06-10 06:18:25 | INFO     | __main__:main:890 -    🌐 WebSocket: ws://************:27027/ws/{client_id}
2025-06-10 06:18:25 | INFO     | __main__:main:891 -    🔍 Health Check: http://************:27027/health
2025-06-10 06:18:25 | INFO     | __main__:main:892 - 
2025-06-10 06:18:25 | INFO     | __main__:main:895 - 🤖 AI Models Status:
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:18:25 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:18:25 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:18:25 | INFO     | __main__:main:911 -    📦 Local Models (2 found):
2025-06-10 06:18:25 | INFO     | __main__:main:914 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-10 06:18:25 | INFO     | __main__:main:914 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-10 06:18:25 | INFO     | __main__:main:922 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-10 06:18:25 | INFO     | __main__:main:926 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-10 06:18:25 | INFO     | __main__:main:932 - 
2025-06-10 06:18:25 | INFO     | __main__:main:933 - ⚙️ Server Configuration:
2025-06-10 06:18:25 | INFO     | __main__:main:934 -    🏠 Host: ************
2025-06-10 06:18:25 | INFO     | __main__:main:935 -    🔌 Port: 27027
2025-06-10 06:18:25 | INFO     | __main__:main:936 -    🐛 Debug Mode: ✅ Enabled
2025-06-10 06:18:25 | INFO     | __main__:main:937 -    📝 Log Level: DEBUG
2025-06-10 06:18:25 | INFO     | __main__:main:938 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-10 06:18:25 | INFO     | __main__:main:941 - 
2025-06-10 06:18:25 | INFO     | __main__:main:942 - 🎮 Integrated CLI Commands (via API):
2025-06-10 06:18:25 | INFO     | __main__:main:943 -    • POST /api/cli                 - Execute CLI commands
2025-06-10 06:18:25 | INFO     | __main__:main:944 -    • GET /api/cli/help             - Get CLI help
2025-06-10 06:18:25 | INFO     | __main__:main:945 -    • Available commands: help, models, config, test, health, system
2025-06-10 06:18:25 | INFO     | __main__:main:946 -    • Example: POST /api/cli {'command': 'models list'}
2025-06-10 06:18:25 | INFO     | __main__:main:947 -    • Example: POST /api/cli {'command': 'test chat Hello'}
2025-06-10 06:18:25 | INFO     | __main__:main:950 - 
2025-06-10 06:18:25 | INFO     | __main__:main:951 - 🎯 Quick Start Guide:
2025-06-10 06:18:25 | INFO     | __main__:main:952 -    1. 🖥️ Start RCS IDE Client Application
2025-06-10 06:18:25 | INFO     | __main__:main:953 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-10 06:18:25 | INFO     | __main__:main:954 -    3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}
2025-06-10 06:18:25 | INFO     | __main__:main:955 -    4. 💻 Begin coding with AI assistance!
2025-06-10 06:18:25 | INFO     | __main__:main:956 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-10 06:18:25 | INFO     | __main__:main:958 - ================================================================================
2025-06-10 06:18:25 | INFO     | __main__:main:959 - 🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.
2025-06-10 06:18:25 | INFO     | __main__:main:960 - ================================================================================
2025-06-10 06:18:25 | INFO     | __main__:lifespan:212 - Starting Reverie Code Studio Server...
2025-06-10 06:18:25 | INFO     | app.services.ai_service:initialize:50 - Initializing AI Service...
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:18:25 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:18:25 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:18:25 | INFO     | app.services.model_manager:load_model:237 - Loading model: Qwen3-0.6B
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_load_transformers_model:273 - Model path: data\models\Qwen3-0.6B
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_load_transformers_model:274 - Loading from: Local directory
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_load_transformers_model:282 - ✅ Accelerate available - using device mapping
2025-06-10 06:18:25 | WARNING  | app.services.model_manager:_load_transformers_model:299 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_load_transformers_model:302 - Loading tokenizer...
2025-06-10 06:18:25 | INFO     | app.services.model_manager:_load_transformers_model:328 - Loading model...
2025-06-10 06:18:37 | INFO     | app.services.model_manager:_load_transformers_model:339 - ✅ Model loaded successfully: Qwen3-0.6B
2025-06-10 06:18:37 | INFO     | app.services.model_manager:_load_transformers_model:340 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-10 06:18:37 | INFO     | app.services.model_manager:_load_transformers_model:346 - 🔋 GPU Memory - Allocated: 1.11GB, Reserved: 1.42GB
2025-06-10 06:18:37 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-10 06:18:37 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-10 06:18:37 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-10 06:18:37 | INFO     | app.services.ai_service:initialize:64 - AI Service initialization complete
2025-06-10 06:18:37 | INFO     | app.services.tools_manager:initialize:31 - Initializing Tools Manager...
2025-06-10 06:18:37 | INFO     | app.services.enhanced_web_search:initialize:33 - Initializing Enhanced Web Search Service...
2025-06-10 06:18:39 | INFO     | app.services.enhanced_web_search:initialize:43 - ✅ duckduckgo search engine available
2025-06-10 06:18:43 | INFO     | app.services.enhanced_web_search:initialize:49 - Default search engine: duckduckgo
2025-06-10 06:18:43 | INFO     | app.services.tools_manager:initialize:43 - Registered 10 tools
2025-06-10 06:18:43 | INFO     | app.services.enhanced_ai_service:initialize:32 - Enhanced AI Service initialized with tool calling
2025-06-10 06:18:43 | INFO     | __main__:lifespan:217 - AI Service initialized
2025-06-10 06:18:43 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-10 06:18:43 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:18:43 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:18:43 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:18:43 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:18:43 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:18:43 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:18:43 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-10 06:18:43 | INFO     | __main__:initialize:600 - 🎮 CLI interface initialized
2025-06-10 06:18:43 | INFO     | __main__:input_loop:612 - 🚀 CLI interface ready - type commands below:
2025-06-10 06:18:43 | INFO     | __main__:lifespan:233 - Integrated CLI started
2025-06-10 06:18:43 | INFO     | __main__:input_loop:613 - 💡 Available commands: help, models, config, test, health, system, exit
2025-06-10 06:18:43 | INFO     | __main__:lifespan:237 - Server startup complete
2025-06-10 06:18:48 | ERROR    | __main__:input_loop:638 - ❌ CLI input error: There is no current event loop in thread 'Thread-1 (input_loop)'.
2025-06-10 06:29:13 | INFO     | __main__:main:863 - Starting Rilance Code Studio Server with Integrated CLI...
2025-06-10 06:29:13 | INFO     | __main__:main:880 - ================================================================================
2025-06-10 06:29:13 | INFO     | __main__:main:881 - 🚀 RILANCE CODE STUDIO UNIFIED SERVER
2025-06-10 06:29:13 | INFO     | __main__:main:882 - ================================================================================
2025-06-10 06:29:13 | INFO     | __main__:main:883 - 📍 Server Host: ************
2025-06-10 06:29:13 | INFO     | __main__:main:884 - 🔌 Server Port: 27027
2025-06-10 06:29:13 | INFO     | __main__:main:885 - 🌐 Primary IP: ************
2025-06-10 06:29:13 | INFO     | __main__:main:886 - 🎮 CLI Interface: ✅ Integrated via API
2025-06-10 06:29:13 | INFO     | __main__:main:887 - 
2025-06-10 06:29:13 | INFO     | __main__:main:890 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-10 06:29:13 | INFO     | __main__:main:891 -    ┌─────────────────────────────────────────────────────────┐
2025-06-10 06:29:13 | INFO     | __main__:main:892 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-10 06:29:13 | INFO     | __main__:main:893 -    │                                                         │
2025-06-10 06:29:13 | INFO     | __main__:main:896 -    │  1. Server IP: ************:27027 (PRIMARY)      │
2025-06-10 06:29:13 | INFO     | __main__:main:897 -    │                                                         │
2025-06-10 06:29:13 | INFO     | __main__:main:898 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-10 06:29:13 | INFO     | __main__:main:899 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-10 06:29:13 | INFO     | __main__:main:900 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-10 06:29:13 | INFO     | __main__:main:901 -    └─────────────────────────────────────────────────────────┘
2025-06-10 06:29:13 | INFO     | __main__:main:902 - 
2025-06-10 06:29:13 | INFO     | __main__:main:904 - 📡 All Available Network Addresses:
2025-06-10 06:29:13 | INFO     | __main__:main:906 -    • IP: ************
2025-06-10 06:29:13 | INFO     | __main__:main:907 - 
2025-06-10 06:29:13 | INFO     | __main__:main:908 - 📋 Web Interface URLs:
2025-06-10 06:29:13 | INFO     | __main__:main:909 -    🏠 Main Page: http://************:27027/
2025-06-10 06:29:13 | INFO     | __main__:main:910 -    📖 Documentation: http://************:27027/static/index.html
2025-06-10 06:29:13 | INFO     | __main__:main:911 -    ❤️  Health Check: http://************:27027/health
2025-06-10 06:29:13 | INFO     | __main__:main:912 -    📚 API Docs: http://************:27027/api/docs
2025-06-10 06:29:13 | INFO     | __main__:main:913 - 
2025-06-10 06:29:13 | INFO     | __main__:main:915 - 🔌 API Endpoints for Client:
2025-06-10 06:29:13 | INFO     | __main__:main:916 -    📡 HTTP API: http://************:27027/api/
2025-06-10 06:29:13 | INFO     | __main__:main:917 -    🌐 WebSocket: ws://************:27027/ws/{client_id}
2025-06-10 06:29:13 | INFO     | __main__:main:918 -    🔍 Health Check: http://************:27027/health
2025-06-10 06:29:13 | INFO     | __main__:main:919 - 
2025-06-10 06:29:13 | INFO     | __main__:main:922 - 🤖 AI Models Status:
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:29:13 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:29:13 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:29:13 | INFO     | __main__:main:938 -    📦 Local Models (2 found):
2025-06-10 06:29:13 | INFO     | __main__:main:941 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-10 06:29:13 | INFO     | __main__:main:941 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-10 06:29:13 | INFO     | __main__:main:949 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-10 06:29:13 | INFO     | __main__:main:953 -    🤗 HuggingFace Models: 8 predefined models available
2025-06-10 06:29:13 | INFO     | __main__:main:959 - 
2025-06-10 06:29:13 | INFO     | __main__:main:960 - ⚙️ Server Configuration:
2025-06-10 06:29:13 | INFO     | __main__:main:961 -    🏠 Host: ************
2025-06-10 06:29:13 | INFO     | __main__:main:962 -    🔌 Port: 27027
2025-06-10 06:29:13 | INFO     | __main__:main:963 -    🐛 Debug Mode: ✅ Enabled
2025-06-10 06:29:13 | INFO     | __main__:main:964 -    📝 Log Level: DEBUG
2025-06-10 06:29:13 | INFO     | __main__:main:965 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-10 06:29:13 | INFO     | __main__:main:968 - 
2025-06-10 06:29:13 | INFO     | __main__:main:969 - 🎮 Integrated CLI Commands (via API):
2025-06-10 06:29:13 | INFO     | __main__:main:970 -    • POST /api/cli                 - Execute CLI commands
2025-06-10 06:29:13 | INFO     | __main__:main:971 -    • GET /api/cli/help             - Get CLI help
2025-06-10 06:29:13 | INFO     | __main__:main:972 -    • Available commands: help, models, config, test, health, system
2025-06-10 06:29:13 | INFO     | __main__:main:973 -    • Example: POST /api/cli {'command': 'models list'}
2025-06-10 06:29:13 | INFO     | __main__:main:974 -    • Example: POST /api/cli {'command': 'test chat Hello'}
2025-06-10 06:29:13 | INFO     | __main__:main:977 - 
2025-06-10 06:29:13 | INFO     | __main__:main:978 - 🎯 Quick Start Guide:
2025-06-10 06:29:13 | INFO     | __main__:main:979 -    1. 🖥️ Start RCS IDE Client Application
2025-06-10 06:29:13 | INFO     | __main__:main:980 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-10 06:29:13 | INFO     | __main__:main:981 -    3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}
2025-06-10 06:29:13 | INFO     | __main__:main:982 -    4. 💻 Begin coding with AI assistance!
2025-06-10 06:29:13 | INFO     | __main__:main:983 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-10 06:29:13 | INFO     | __main__:main:985 - ================================================================================
2025-06-10 06:29:13 | INFO     | __main__:main:986 - 🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.
2025-06-10 06:29:13 | INFO     | __main__:main:987 - ================================================================================
2025-06-10 06:29:13 | INFO     | __main__:lifespan:212 - Starting Reverie Code Studio Server...
2025-06-10 06:29:13 | INFO     | app.services.ai_service:initialize:50 - Initializing AI Service...
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:29:13 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:29:13 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:29:13 | INFO     | app.services.model_manager:load_model:237 - Loading model: Qwen3-0.6B
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_load_transformers_model:273 - Model path: data\models\Qwen3-0.6B
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_load_transformers_model:274 - Loading from: Local directory
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_load_transformers_model:282 - ✅ Accelerate available - using device mapping
2025-06-10 06:29:13 | WARNING  | app.services.model_manager:_load_transformers_model:299 - ⚠️ BitsAndBytes not available, loading without quantization
2025-06-10 06:29:13 | INFO     | app.services.model_manager:_load_transformers_model:302 - Loading tokenizer...
2025-06-10 06:29:14 | INFO     | app.services.model_manager:_load_transformers_model:328 - Loading model...
2025-06-10 06:29:15 | INFO     | app.services.model_manager:_load_transformers_model:339 - ✅ Model loaded successfully: Qwen3-0.6B
2025-06-10 06:29:15 | INFO     | app.services.model_manager:_load_transformers_model:340 - 📊 Model info - Device: cuda, Type: Qwen3ForCausalLM
2025-06-10 06:29:15 | INFO     | app.services.model_manager:_load_transformers_model:346 - 🔋 GPU Memory - Allocated: 1.11GB, Reserved: 1.42GB
2025-06-10 06:29:15 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-10 06:29:15 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-10 06:29:15 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-10 06:29:15 | INFO     | app.services.ai_service:initialize:64 - AI Service initialization complete
2025-06-10 06:29:15 | INFO     | app.services.tools_manager:initialize:31 - Initializing Tools Manager...
2025-06-10 06:29:15 | INFO     | app.services.enhanced_web_search:initialize:33 - Initializing Enhanced Web Search Service...
2025-06-10 06:29:15 | INFO     | app.services.enhanced_web_search:initialize:43 - ✅ duckduckgo search engine available
2025-06-10 06:29:20 | DEBUG    | app.services.enhanced_web_search:_search_searx:319 - SearX instance https://searx.xyz failed: Expecting value: line 1 column 1 (char 0)
2025-06-10 06:29:20 | INFO     | app.services.enhanced_web_search:initialize:49 - Default search engine: duckduckgo
2025-06-10 06:29:20 | INFO     | app.services.tools_manager:initialize:43 - Registered 10 tools
2025-06-10 06:29:20 | INFO     | app.services.enhanced_ai_service:initialize:32 - Enhanced AI Service initialized with tool calling
2025-06-10 06:29:20 | INFO     | __main__:lifespan:217 - AI Service initialized
2025-06-10 06:29:20 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-10 06:29:20 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-10 06:29:20 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-10 06:29:20 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-10 06:29:20 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-10 06:29:20 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-10 06:29:20 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-10 06:29:20 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-10 06:29:20 | INFO     | __main__:initialize:600 - 🎮 CLI interface initialized
2025-06-10 06:29:20 | INFO     | __main__:input_loop:612 - 🚀 CLI interface ready - type commands below:
2025-06-10 06:29:20 | INFO     | __main__:lifespan:233 - Integrated CLI started
2025-06-10 06:29:20 | INFO     | __main__:input_loop:613 - 💡 Available commands: help, models, config, test, health, system, exit
2025-06-10 06:29:20 | INFO     | __main__:lifespan:237 - Server startup complete
2025-06-10 06:29:43 | WARNING  | __main__:process_command_sync:662 - ⚠️ Command 'models' requires async execution, use API endpoint instead
2025-06-16 06:47:15 | INFO     | __main__:main:875 - Starting Rilance Code Studio Server with Integrated CLI...
2025-06-16 06:47:15 | INFO     | __main__:main:892 - ================================================================================
2025-06-16 06:47:15 | INFO     | __main__:main:893 - 🚀 RILANCE CODE STUDIO UNIFIED SERVER
2025-06-16 06:47:15 | INFO     | __main__:main:894 - ================================================================================
2025-06-16 06:47:15 | INFO     | __main__:main:895 - 📍 Server Host: 127.0.0.1
2025-06-16 06:47:15 | INFO     | __main__:main:896 - 🔌 Server Port: 27027
2025-06-16 06:47:15 | INFO     | __main__:main:897 - 🌐 Primary IP: 127.0.0.1
2025-06-16 06:47:15 | INFO     | __main__:main:898 - 🎮 CLI Interface: ✅ Integrated via API
2025-06-16 06:47:15 | INFO     | __main__:main:899 - 
2025-06-16 06:47:15 | INFO     | __main__:main:902 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-16 06:47:15 | INFO     | __main__:main:903 -    ┌─────────────────────────────────────────────────────────┐
2025-06-16 06:47:15 | INFO     | __main__:main:904 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-16 06:47:15 | INFO     | __main__:main:905 -    │                                                         │
2025-06-16 06:47:15 | INFO     | __main__:main:908 -    │  1. Server IP: 127.0.0.1:27027 (PRIMARY)      │
2025-06-16 06:47:15 | INFO     | __main__:main:909 -    │                                                         │
2025-06-16 06:47:15 | INFO     | __main__:main:910 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-16 06:47:15 | INFO     | __main__:main:911 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-16 06:47:15 | INFO     | __main__:main:912 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-16 06:47:15 | INFO     | __main__:main:913 -    └─────────────────────────────────────────────────────────┘
2025-06-16 06:47:15 | INFO     | __main__:main:914 - 
2025-06-16 06:47:15 | INFO     | __main__:main:916 - 📡 All Available Network Addresses:
2025-06-16 06:47:15 | INFO     | __main__:main:918 -    • IP: 127.0.0.1
2025-06-16 06:47:15 | INFO     | __main__:main:919 - 
2025-06-16 06:47:15 | INFO     | __main__:main:920 - 📋 Web Interface URLs:
2025-06-16 06:47:15 | INFO     | __main__:main:921 -    🏠 Main Page: http://127.0.0.1:27027/
2025-06-16 06:47:15 | INFO     | __main__:main:922 -    📖 Documentation: http://127.0.0.1:27027/static/index.html
2025-06-16 06:47:15 | INFO     | __main__:main:923 -    ❤️  Health Check: http://127.0.0.1:27027/health
2025-06-16 06:47:15 | INFO     | __main__:main:924 -    📚 API Docs: http://127.0.0.1:27027/api/docs
2025-06-16 06:47:15 | INFO     | __main__:main:925 - 
2025-06-16 06:47:15 | INFO     | __main__:main:927 - 🔌 API Endpoints for Client:
2025-06-16 06:47:15 | INFO     | __main__:main:928 -    📡 HTTP API: http://127.0.0.1:27027/api/
2025-06-16 06:47:15 | INFO     | __main__:main:929 -    🌐 WebSocket: ws://127.0.0.1:27027/ws/{client_id}
2025-06-16 06:47:15 | INFO     | __main__:main:930 -    🔍 Health Check: http://127.0.0.1:27027/health
2025-06-16 06:47:15 | INFO     | __main__:main:931 - 
2025-06-16 06:47:15 | INFO     | __main__:main:934 - 🤖 AI Models Status:
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:15 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:15 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:15 | INFO     | __main__:main:950 -    📦 Local Models (2 found):
2025-06-16 06:47:15 | INFO     | __main__:main:953 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-16 06:47:15 | INFO     | __main__:main:953 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-16 06:47:15 | INFO     | __main__:main:961 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-16 06:47:15 | INFO     | __main__:main:965 -    🤗 HuggingFace Models: 3 predefined models available
2025-06-16 06:47:15 | INFO     | __main__:main:971 - 
2025-06-16 06:47:15 | INFO     | __main__:main:972 - ⚙️ Server Configuration:
2025-06-16 06:47:15 | INFO     | __main__:main:973 -    🏠 Host: 127.0.0.1
2025-06-16 06:47:15 | INFO     | __main__:main:974 -    🔌 Port: 27027
2025-06-16 06:47:15 | INFO     | __main__:main:975 -    🐛 Debug Mode: ✅ Enabled
2025-06-16 06:47:15 | INFO     | __main__:main:976 -    📝 Log Level: DEBUG
2025-06-16 06:47:15 | INFO     | __main__:main:977 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-16 06:47:15 | INFO     | __main__:main:980 - 
2025-06-16 06:47:15 | INFO     | __main__:main:981 - 🎮 Integrated CLI Commands (via API):
2025-06-16 06:47:15 | INFO     | __main__:main:982 -    • POST /api/cli                 - Execute CLI commands
2025-06-16 06:47:15 | INFO     | __main__:main:983 -    • GET /api/cli/help             - Get CLI help
2025-06-16 06:47:15 | INFO     | __main__:main:984 -    • Available commands: help, models, config, test, health, system
2025-06-16 06:47:15 | INFO     | __main__:main:985 -    • Example: POST /api/cli {'command': 'models list'}
2025-06-16 06:47:15 | INFO     | __main__:main:986 -    • Example: POST /api/cli {'command': 'test chat Hello'}
2025-06-16 06:47:15 | INFO     | __main__:main:989 - 
2025-06-16 06:47:15 | INFO     | __main__:main:990 - 🎯 Quick Start Guide:
2025-06-16 06:47:15 | INFO     | __main__:main:991 -    1. 🖥️ Start RCS IDE Client Application
2025-06-16 06:47:15 | INFO     | __main__:main:992 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-16 06:47:15 | INFO     | __main__:main:993 -    3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}
2025-06-16 06:47:15 | INFO     | __main__:main:994 -    4. 💻 Begin coding with AI assistance!
2025-06-16 06:47:15 | INFO     | __main__:main:995 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-16 06:47:15 | INFO     | __main__:main:997 - ================================================================================
2025-06-16 06:47:15 | INFO     | __main__:main:998 - 🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.
2025-06-16 06:47:15 | INFO     | __main__:main:999 - ================================================================================
2025-06-16 06:47:15 | INFO     | __main__:lifespan:212 - Starting Reverie Code Studio Server...
2025-06-16 06:47:15 | INFO     | app.services.ai_service:initialize:50 - Initializing AI Service...
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:15 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:15 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:15 | INFO     | app.services.model_manager:load_model:173 - Loading model: Qwen/Qwen1.5-4B
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_load_transformers_model:209 - Model path: Qwen/Qwen1.5-4B
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_load_transformers_model:210 - Loading from: HuggingFace Hub
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_load_transformers_model:218 - ✅ Accelerate available - using device mapping
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_load_transformers_model:233 - ✅ BitsAndBytes quantization enabled
2025-06-16 06:47:15 | INFO     | app.services.model_manager:_load_transformers_model:238 - Loading tokenizer...
2025-06-16 06:47:15 | ERROR    | app.services.model_manager:_load_transformers_model:287 - ❌ Failed to load transformers model: We couldn't connect to 'https://huggingface.co' to load the files, and couldn't find them in the cached files.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
2025-06-16 06:47:16 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-16 06:47:16 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-16 06:47:16 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-16 06:47:16 | INFO     | app.services.ai_service:initialize:64 - AI Service initialization complete
2025-06-16 06:47:16 | INFO     | app.services.tools_manager:initialize:31 - Initializing Tools Manager...
2025-06-16 06:47:16 | INFO     | app.services.enhanced_web_search:initialize:33 - Initializing Enhanced Web Search Service...
2025-06-16 06:47:16 | INFO     | app.services.enhanced_web_search:initialize:43 - ✅ duckduckgo search engine available
2025-06-16 06:47:21 | DEBUG    | app.services.enhanced_web_search:_search_searx:319 - SearX instance https://searx.xyz failed: Expecting value: line 1 column 1 (char 0)
2025-06-16 06:47:21 | INFO     | app.services.enhanced_web_search:initialize:49 - Default search engine: duckduckgo
2025-06-16 06:47:21 | INFO     | app.services.tools_manager:initialize:43 - Registered 10 tools
2025-06-16 06:47:21 | INFO     | app.services.enhanced_ai_service:initialize:33 - Enhanced AI Service initialized with tool calling
2025-06-16 06:47:21 | INFO     | __main__:lifespan:217 - AI Service initialized
2025-06-16 06:47:21 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-16 06:47:21 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:21 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:21 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:21 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:21 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:21 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:21 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-16 06:47:21 | INFO     | __main__:initialize:612 - 🎮 CLI interface initialized
2025-06-16 06:47:21 | INFO     | __main__:input_loop:624 - 🚀 CLI interface ready - type commands below:
2025-06-16 06:47:21 | INFO     | __main__:lifespan:233 - Integrated CLI started
2025-06-16 06:47:21 | INFO     | __main__:input_loop:625 - 💡 Available commands: help, models, config, test, health, system, exit
2025-06-16 06:47:21 | INFO     | __main__:lifespan:237 - Server startup complete
2025-06-16 06:47:36 | INFO     | __main__:main:875 - Starting Rilance Code Studio Server with Integrated CLI...
2025-06-16 06:47:36 | INFO     | __main__:main:892 - ================================================================================
2025-06-16 06:47:36 | INFO     | __main__:main:893 - 🚀 RILANCE CODE STUDIO UNIFIED SERVER
2025-06-16 06:47:36 | INFO     | __main__:main:894 - ================================================================================
2025-06-16 06:47:36 | INFO     | __main__:main:895 - 📍 Server Host: 127.0.0.1
2025-06-16 06:47:36 | INFO     | __main__:main:896 - 🔌 Server Port: 27027
2025-06-16 06:47:36 | INFO     | __main__:main:897 - 🌐 Primary IP: 127.0.0.1
2025-06-16 06:47:36 | INFO     | __main__:main:898 - 🎮 CLI Interface: ✅ Integrated via API
2025-06-16 06:47:36 | INFO     | __main__:main:899 - 
2025-06-16 06:47:36 | INFO     | __main__:main:902 - 🎯 CLIENT CONNECTION INFORMATION:
2025-06-16 06:47:36 | INFO     | __main__:main:903 -    ┌─────────────────────────────────────────────────────────┐
2025-06-16 06:47:36 | INFO     | __main__:main:904 -    │  📱 RCS IDE Client Connection Settings:                │
2025-06-16 06:47:36 | INFO     | __main__:main:905 -    │                                                         │
2025-06-16 06:47:36 | INFO     | __main__:main:908 -    │  1. Server IP: 127.0.0.1:27027 (PRIMARY)      │
2025-06-16 06:47:36 | INFO     | __main__:main:909 -    │                                                         │
2025-06-16 06:47:36 | INFO     | __main__:main:910 -    │  💡 Use any of the above IP addresses in your client   │
2025-06-16 06:47:36 | INFO     | __main__:main:911 -    │     Enter in format: IP:PORT (e.g., {ip}:{args.port})     │
2025-06-16 06:47:36 | INFO     | __main__:main:912 -    │  ⚙️  Configuration: config.json (host: {config_host})    │
2025-06-16 06:47:36 | INFO     | __main__:main:913 -    └─────────────────────────────────────────────────────────┘
2025-06-16 06:47:36 | INFO     | __main__:main:914 - 
2025-06-16 06:47:36 | INFO     | __main__:main:916 - 📡 All Available Network Addresses:
2025-06-16 06:47:36 | INFO     | __main__:main:918 -    • IP: 127.0.0.1
2025-06-16 06:47:36 | INFO     | __main__:main:919 - 
2025-06-16 06:47:36 | INFO     | __main__:main:920 - 📋 Web Interface URLs:
2025-06-16 06:47:36 | INFO     | __main__:main:921 -    🏠 Main Page: http://127.0.0.1:27027/
2025-06-16 06:47:36 | INFO     | __main__:main:922 -    📖 Documentation: http://127.0.0.1:27027/static/index.html
2025-06-16 06:47:36 | INFO     | __main__:main:923 -    ❤️  Health Check: http://127.0.0.1:27027/health
2025-06-16 06:47:36 | INFO     | __main__:main:924 -    📚 API Docs: http://127.0.0.1:27027/api/docs
2025-06-16 06:47:36 | INFO     | __main__:main:925 - 
2025-06-16 06:47:36 | INFO     | __main__:main:927 - 🔌 API Endpoints for Client:
2025-06-16 06:47:36 | INFO     | __main__:main:928 -    📡 HTTP API: http://127.0.0.1:27027/api/
2025-06-16 06:47:36 | INFO     | __main__:main:929 -    🌐 WebSocket: ws://127.0.0.1:27027/ws/{client_id}
2025-06-16 06:47:36 | INFO     | __main__:main:930 -    🔍 Health Check: http://127.0.0.1:27027/health
2025-06-16 06:47:36 | INFO     | __main__:main:931 - 
2025-06-16 06:47:36 | INFO     | __main__:main:934 - 🤖 AI Models Status:
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:36 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:36 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:36 | INFO     | __main__:main:950 -    📦 Local Models (2 found):
2025-06-16 06:47:36 | INFO     | __main__:main:953 -       ✅ DeepSeek-R1-0528-Qwen3-8B (15.3GB)
2025-06-16 06:47:36 | INFO     | __main__:main:953 -       ✅ Qwen3-0.6B (2.8GB)
2025-06-16 06:47:36 | INFO     | __main__:main:961 -    🌐 OpenAI Models: ❌ No API key configured
2025-06-16 06:47:36 | INFO     | __main__:main:965 -    🤗 HuggingFace Models: 3 predefined models available
2025-06-16 06:47:36 | INFO     | __main__:main:971 - 
2025-06-16 06:47:36 | INFO     | __main__:main:972 - ⚙️ Server Configuration:
2025-06-16 06:47:36 | INFO     | __main__:main:973 -    🏠 Host: 127.0.0.1
2025-06-16 06:47:36 | INFO     | __main__:main:974 -    🔌 Port: 27027
2025-06-16 06:47:36 | INFO     | __main__:main:975 -    🐛 Debug Mode: ✅ Enabled
2025-06-16 06:47:36 | INFO     | __main__:main:976 -    📝 Log Level: DEBUG
2025-06-16 06:47:36 | INFO     | __main__:main:977 -    📁 Log Directory: H:\RIL\RCS\server\logs
2025-06-16 06:47:36 | INFO     | __main__:main:980 - 
2025-06-16 06:47:36 | INFO     | __main__:main:981 - 🎮 Integrated CLI Commands (via API):
2025-06-16 06:47:36 | INFO     | __main__:main:982 -    • POST /api/cli                 - Execute CLI commands
2025-06-16 06:47:36 | INFO     | __main__:main:983 -    • GET /api/cli/help             - Get CLI help
2025-06-16 06:47:36 | INFO     | __main__:main:984 -    • Available commands: help, models, config, test, health, system
2025-06-16 06:47:36 | INFO     | __main__:main:985 -    • Example: POST /api/cli {'command': 'models list'}
2025-06-16 06:47:36 | INFO     | __main__:main:986 -    • Example: POST /api/cli {'command': 'test chat Hello'}
2025-06-16 06:47:36 | INFO     | __main__:main:989 - 
2025-06-16 06:47:36 | INFO     | __main__:main:990 - 🎯 Quick Start Guide:
2025-06-16 06:47:36 | INFO     | __main__:main:991 -    1. 🖥️ Start RCS IDE Client Application
2025-06-16 06:47:36 | INFO     | __main__:main:992 -    2. 🔗 Enter server IP in client (use one from above)
2025-06-16 06:47:36 | INFO     | __main__:main:993 -    3. 🤖 Load a model using CLI API: POST /api/cli {'command': 'models load <name>'}
2025-06-16 06:47:36 | INFO     | __main__:main:994 -    4. 💻 Begin coding with AI assistance!
2025-06-16 06:47:36 | INFO     | __main__:main:995 -    5. 📖 View docs: http://{local_ip}:{args.port}/static/index.html
2025-06-16 06:47:36 | INFO     | __main__:main:997 - ================================================================================
2025-06-16 06:47:36 | INFO     | __main__:main:998 - 🎉 UNIFIED SERVER READY! Web API + CLI integrated via API endpoints.
2025-06-16 06:47:36 | INFO     | __main__:main:999 - ================================================================================
2025-06-16 06:47:36 | INFO     | __main__:lifespan:212 - Starting Reverie Code Studio Server...
2025-06-16 06:47:36 | INFO     | app.services.ai_service:initialize:50 - Initializing AI Service...
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:36 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:36 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:36 | INFO     | app.services.model_manager:load_model:173 - Loading model: Qwen/Qwen1.5-4B
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_load_transformers_model:209 - Model path: Qwen/Qwen1.5-4B
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_load_transformers_model:210 - Loading from: HuggingFace Hub
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_load_transformers_model:218 - ✅ Accelerate available - using device mapping
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_load_transformers_model:233 - ✅ BitsAndBytes quantization enabled
2025-06-16 06:47:36 | INFO     | app.services.model_manager:_load_transformers_model:238 - Loading tokenizer...
2025-06-16 06:47:36 | ERROR    | app.services.model_manager:_load_transformers_model:287 - ❌ Failed to load transformers model: We couldn't connect to 'https://huggingface.co' to load the files, and couldn't find them in the cached files.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
2025-06-16 06:47:36 | INFO     | app.services.web_search_service:initialize:24 - Web search service initialized
2025-06-16 06:47:36 | INFO     | app.services.file_service:initialize:27 - File service initialized
2025-06-16 06:47:36 | INFO     | app.services.code_analysis_service:initialize:26 - Code analysis service initialized
2025-06-16 06:47:36 | INFO     | app.services.ai_service:initialize:64 - AI Service initialization complete
2025-06-16 06:47:36 | INFO     | app.services.tools_manager:initialize:31 - Initializing Tools Manager...
2025-06-16 06:47:36 | INFO     | app.services.enhanced_web_search:initialize:33 - Initializing Enhanced Web Search Service...
2025-06-16 06:47:37 | INFO     | app.services.enhanced_web_search:initialize:43 - ✅ duckduckgo search engine available
2025-06-16 06:47:41 | DEBUG    | app.services.enhanced_web_search:_search_searx:319 - SearX instance https://searx.xyz failed: Expecting value: line 1 column 1 (char 0)
2025-06-16 06:47:41 | INFO     | app.services.enhanced_web_search:initialize:49 - Default search engine: duckduckgo
2025-06-16 06:47:41 | INFO     | app.services.tools_manager:initialize:43 - Registered 10 tools
2025-06-16 06:47:41 | INFO     | app.services.enhanced_ai_service:initialize:33 - Enhanced AI Service initialized with tool calling
2025-06-16 06:47:41 | INFO     | __main__:lifespan:217 - AI Service initialized
2025-06-16 06:47:41 | INFO     | cli:initialize:113 - 🔧 Initializing model manager...
2025-06-16 06:47:41 | INFO     | app.services.model_manager:_get_local_models:56 - 🔍 Scanning for local models in: data\models
2025-06-16 06:47:41 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: DeepSeek-R1-0528-Qwen3-8B (available)
2025-06-16 06:47:41 | INFO     | app.services.model_manager:_get_local_models:86 - 📦 Found local model: Qwen3-0.6B (available)
2025-06-16 06:47:41 | INFO     | app.services.model_manager:_get_local_models:110 - 📊 Total local models found: 2
2025-06-16 06:47:41 | INFO     | app.services.model_manager:refresh_models:49 - Refreshed models: 3 categories
2025-06-16 06:47:41 | INFO     | app.services.model_manager:initialize:40 - Model manager initialized
2025-06-16 06:47:41 | INFO     | cli:initialize:115 - ✅ Model manager initialized
2025-06-16 06:47:41 | INFO     | __main__:initialize:612 - 🎮 CLI interface initialized
2025-06-16 06:47:41 | INFO     | __main__:input_loop:624 - 🚀 CLI interface ready - type commands below:
2025-06-16 06:47:41 | INFO     | __main__:lifespan:233 - Integrated CLI started
2025-06-16 06:47:41 | INFO     | __main__:input_loop:625 - 💡 Available commands: help, models, config, test, health, system, exit
2025-06-16 06:47:41 | INFO     | __main__:lifespan:237 - Server startup complete
2025-06-16 06:47:41 | INFO     | __main__:lifespan:242 - Shutting down Reverie Code Studio Server...
2025-06-16 06:47:42 | INFO     | app.services.model_manager:unload_model:308 - Model unloaded successfully
