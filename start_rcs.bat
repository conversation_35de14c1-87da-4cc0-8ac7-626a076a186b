@echo off
echo ========================================
echo    Reverie Code Studio - Startup
echo ========================================
echo.

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

:: Set working directory to script location
cd /d "%~dp0"

echo 🔧 Setting up environment...

:: Start server in background
echo 📡 Starting RCS Server...
start "RCS Server" cmd /k "cd server && python main.py"

:: Wait a moment for server to start
echo ⏳ Waiting for server to initialize...
timeout /t 5 /nobreak >nul

:: Test server connection
echo 🔍 Testing server connection...
python test_connection.py
if errorlevel 1 (
    echo ⚠️  Server connection test failed
    echo Please check server logs for errors
    pause
)

:: Start client
echo 🖥️  Starting RCS Client...
cd client
python main.py

echo.
echo 👋 RCS session ended
pause
