"""
Cursor-inspired UI styles for RCS IDE
"""

# Cursor-inspired color scheme - Modern and clean
COPILOT_COLORS = {
    # Background colors - Darker, more modern like Cursor
    'bg_primary': '#0e1117',       # Main background (darker)
    'bg_secondary': '#131721',     # Secondary background
    'bg_tertiary': '#1c2333',      # Tertiary background
    'bg_hover': '#252e42',         # Hover state
    'bg_active': '#0078d4',        # Active/selected state (Cursor blue)

    # Border colors - Subtler like Cursor
    'border_primary': '#2a3343',   # Main borders (subtler)
    'border_focus': '#0078d4',     # Focus borders (Cursor blue)
    'border_hover': '#3a4559',     # Hover borders

    # Text colors - Better contrast
    'text_primary': '#ffffff',     # Primary text (brighter)
    'text_secondary': '#d1d5db',   # Secondary text
    'text_muted': '#9ca3af',       # Muted text
    'text_disabled': '#6b7280',    # Disabled text

    # Accent colors - Cursor style
    'accent_blue': '#0078d4',      # Primary blue (Cursor blue)
    'accent_blue_hover': '#0066b5', # Blue hover
    'accent_blue_pressed': '#005494', # Blue pressed
    'accent_green': '#4caf50',     # Success green
    'accent_red': '#f44336',       # Error red
    'accent_yellow': '#ff9800',    # Warning yellow

    # Chat bubble colors - Modern contrast
    'user_bubble': '#0078d4',      # User message bubble (Cursor blue)
    'ai_bubble': '#1c2333',        # AI message bubble
    'ai_bubble_border': '#2a3343', # AI bubble border

    # Code colors - GitHub dark style
    'code_bg': '#0d1117',          # Code background (GitHub dark)
    'code_border': '#2a3343',      # Code border
}

def get_sidebar_style():
    """Get main sidebar stylesheet"""
    return f"""
    QWidget {{
        background-color: {COPILOT_COLORS['bg_primary']};
        color: {COPILOT_COLORS['text_primary']};
        font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    }}
    
    QScrollArea {{
        border: none;
        background-color: transparent;
    }}
    
    QScrollBar:vertical {{
        background-color: {COPILOT_COLORS['bg_primary']};
        width: 6px;
        margin: 0px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COPILOT_COLORS['border_primary']};
        min-height: 20px;
        border-radius: 3px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COPILOT_COLORS['border_hover']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
        background: none;
    }}
    """

def get_header_style():
    """Get header section stylesheet"""
    return f"""
    QFrame#ai_header {{
        background-color: {COPILOT_COLORS['bg_secondary']};
        border-bottom: 1px solid {COPILOT_COLORS['border_primary']};
    }}
    
    QLabel#ai_title {{
        color: {COPILOT_COLORS['text_primary']};
        font-weight: 600;
        font-size: 14px;
    }}
    
    QLabel#model_label {{
        color: {COPILOT_COLORS['text_secondary']};
        font-size: 12px;
    }}
    
    QPushButton#mode_button {{
        background-color: transparent;
        color: {COPILOT_COLORS['text_secondary']};
        border: none;
        border-bottom: 2px solid transparent;
        border-radius: 0px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
        min-height: 36px;
    }}
    
    QPushButton#mode_button:hover {{
        color: {COPILOT_COLORS['text_primary']};
        background-color: {COPILOT_COLORS['bg_hover']};
    }}
    
    QPushButton#mode_button:checked {{
        color: {COPILOT_COLORS['text_primary']};
        border-bottom: 2px solid {COPILOT_COLORS['accent_blue']};
    }}
    
    QFrame#context_display {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        border-radius: 6px;
        padding: 4px;
    }}
    
    QLabel#context_label {{
        color: {COPILOT_COLORS['text_secondary']};
        font-size: 12px;
    }}
    
    QPushButton#small_button {{
        background-color: transparent;
        color: {COPILOT_COLORS['text_secondary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 4px;
        padding: 2px 8px;
        font-size: 11px;
    }}
    
    QPushButton#small_button:hover {{
        background-color: {COPILOT_COLORS['bg_hover']};
        color: {COPILOT_COLORS['text_primary']};
    }}
    
    QListWidget#context_files_list {{
        background-color: transparent;
        border: none;
        color: {COPILOT_COLORS['text_primary']};
        font-size: 11px;
    }}
    """

def get_chat_area_style():
    """Get chat area stylesheet with Cursor-style message bubbles"""
    return f"""
    /* Chat area */
    QScrollArea#chat_scroll {{
        background-color: {COPILOT_COLORS['bg_primary']};
        border: none;
        margin: 0px;
    }}

    QWidget#chat_content {{
        background-color: {COPILOT_COLORS['bg_primary']};
    }}

    /* Message bubbles */
    QFrame#user_message {{
        background-color: {COPILOT_COLORS['user_bubble']};
        border: none;
        border-radius: 12px;
        padding: 12px 16px;
        margin: 8px 16px 8px 48px;
    }}

    QFrame#assistant_message {{
        background-color: {COPILOT_COLORS['ai_bubble']};
        border: 1px solid {COPILOT_COLORS['ai_bubble_border']};
        border-radius: 12px;
        padding: 12px 16px;
        margin: 8px 48px 8px 16px;
    }}

    QLabel#message_text {{
        color: {COPILOT_COLORS['text_primary']};
        font-size: 13px;
        line-height: 1.4;
        background-color: transparent;
        border: none;
    }}

    /* Input area */
    QWidget#input_container {{
        background-color: {COPILOT_COLORS['bg_primary']};
        border-top: 1px solid {COPILOT_COLORS['border_primary']};
        padding: 12px;
    }}

    QTextEdit#message_input {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        color: {COPILOT_COLORS['text_primary']};
        border-radius: 8px;
        padding: 12px;
        font-family: 'Segoe UI', system-ui, sans-serif;
        font-size: 13px;
        line-height: 1.4;
        min-height: 40px;
        max-height: 120px;
    }}

    QTextEdit#message_input:focus {{
        border-color: {COPILOT_COLORS['border_focus']};
        outline: none;
    }}

    /* Send button */
    QPushButton#send_button {{
        background-color: {COPILOT_COLORS['accent_blue']};
        border: none;
        color: {COPILOT_COLORS['text_primary']};
        border-radius: 6px;
        padding: 10px 20px;
        font-weight: 600;
        font-size: 12px;
        margin-left: 8px;
    }}

    QPushButton#send_button:hover {{
        background-color: {COPILOT_COLORS['accent_blue_hover']};
    }}

    QPushButton#send_button:pressed {{
        background-color: {COPILOT_COLORS['accent_blue_pressed']};
    }}

    QPushButton#send_button:disabled {{
        background-color: {COPILOT_COLORS['border_primary']};
        color: {COPILOT_COLORS['text_disabled']};
    }}
    """

def get_model_selector_style():
    """Get model selector stylesheet"""
    return f"""
    QComboBox#model_selector {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        color: {COPILOT_COLORS['text_primary']};
        padding: 8px 12px;
        border-radius: 6px;
        min-height: 24px;
        font-size: 12px;
        margin: 8px 12px;
    }}

    QComboBox#model_selector:hover {{
        border-color: {COPILOT_COLORS['border_focus']};
        background-color: {COPILOT_COLORS['bg_hover']};
    }}

    QComboBox#model_selector:focus {{
        border-color: {COPILOT_COLORS['border_focus']};
        outline: none;
    }}

    QComboBox#model_selector::drop-down {{
        border: none;
        width: 24px;
        padding-right: 4px;
    }}

    QComboBox#model_selector QAbstractItemView {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        color: {COPILOT_COLORS['text_primary']};
        selection-background-color: {COPILOT_COLORS['accent_blue']};
        selection-color: {COPILOT_COLORS['text_primary']};
        border-radius: 6px;
        padding: 4px;
    }}

    QComboBox#model_selector QAbstractItemView::item {{
        padding: 8px 12px;
        border-radius: 4px;
        margin: 1px;
    }}

    QComboBox#model_selector QAbstractItemView::item:hover {{
        background-color: {COPILOT_COLORS['bg_hover']};
    }}

    QComboBox#model_selector QAbstractItemView::item:selected {{
        background-color: {COPILOT_COLORS['accent_blue']};
    }}
    """

def get_connection_widget_style():
    """Get server connection widget stylesheet"""
    return f"""
    QLineEdit {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        color: {COPILOT_COLORS['text_primary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
        font-family: 'Consolas', 'Monaco', monospace;
    }}

    QLineEdit:focus {{
        border-color: {COPILOT_COLORS['border_focus']};
        outline: none;
    }}

    QLineEdit:hover {{
        border-color: {COPILOT_COLORS['border_hover']};
    }}
    """

def get_button_style(variant='primary'):
    """Get button stylesheet with variants"""
    if variant == 'primary':
        return f"""
        QPushButton {{
            background-color: {COPILOT_COLORS['accent_blue']};
            color: {COPILOT_COLORS['text_primary']};
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {COPILOT_COLORS['accent_blue_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {COPILOT_COLORS['accent_blue_pressed']};
        }}
        
        QPushButton:disabled {{
            background-color: {COPILOT_COLORS['border_primary']};
            color: {COPILOT_COLORS['text_disabled']};
        }}
        """
    elif variant == 'secondary':
        return f"""
        QPushButton {{
            background-color: transparent;
            color: {COPILOT_COLORS['text_secondary']};
            border: 1px solid {COPILOT_COLORS['border_primary']};
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {COPILOT_COLORS['bg_hover']};
            border-color: {COPILOT_COLORS['border_hover']};
        }}
        
        QPushButton:checked {{
            background-color: {COPILOT_COLORS['accent_blue']};
            color: {COPILOT_COLORS['text_primary']};
            border-color: {COPILOT_COLORS['accent_blue']};
        }}
        """
    elif variant == 'icon':
        return f"""
        QPushButton {{
            background: transparent;
            border: 1px solid {COPILOT_COLORS['border_primary']};
            border-radius: 6px;
            padding: 6px;
            font-size: 14px;
            min-width: 32px;
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {COPILOT_COLORS['bg_hover']};
            border-color: {COPILOT_COLORS['border_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {COPILOT_COLORS['bg_active']};
        }}
        """

def get_model_selector_style():
    """Get model selector dropdown stylesheet"""
    return f"""
    QComboBox {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        color: {COPILOT_COLORS['text_primary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 12px;
        min-width: 160px;
        min-height: 32px;
    }}
    
    QComboBox:hover {{
        border-color: {COPILOT_COLORS['border_focus']};
    }}
    
    QComboBox:focus {{
        border-color: {COPILOT_COLORS['border_focus']};
        outline: none;
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 24px;
        padding-right: 8px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {COPILOT_COLORS['text_secondary']};
        margin-right: 8px;
    }}
    
    QComboBox QAbstractItemView {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        color: {COPILOT_COLORS['text_primary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 6px;
        selection-background-color: {COPILOT_COLORS['accent_blue']};
        outline: none;
        padding: 4px;
    }}
    
    QComboBox QAbstractItemView::item {{
        padding: 10px 12px;
        border-bottom: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 4px;
        margin: 2px;
    }}
    
    QComboBox QAbstractItemView::item:hover {{
        background-color: {COPILOT_COLORS['bg_hover']};
    }}
    
    QComboBox QAbstractItemView::item:selected {{
        background-color: {COPILOT_COLORS['accent_blue']};
        color: {COPILOT_COLORS['text_primary']};
    }}
    """

def get_chat_bubble_style(is_user=False):
    """Get chat bubble stylesheet"""
    if is_user:
        return f"""
        QFrame {{
            background-color: {COPILOT_COLORS['user_bubble']};
            color: white;
            border-radius: 12px;
            border-top-right-radius: 4px;
            margin-left: 40px;
            margin-right: 10px;
        }}
        """
    else:
        return f"""
        QFrame {{
            background-color: {COPILOT_COLORS['ai_bubble']};
            color: {COPILOT_COLORS['text_primary']};
            border: 1px solid {COPILOT_COLORS['ai_bubble_border']};
            border-radius: 12px;
            border-top-left-radius: 4px;
            margin-right: 40px;
            margin-left: 10px;
        }}
        
        QTextBrowser {{
            background: transparent;
            border: none;
            color: {COPILOT_COLORS['text_primary']};
            selection-background-color: {COPILOT_COLORS['accent_blue']};
            selection-color: white;
        }}
        
        QLabel {{
            color: {COPILOT_COLORS['text_muted']};
        }}
        
        QPushButton {{
            background: transparent;
            border: none;
            color: {COPILOT_COLORS['text_muted']};
            border-radius: 12px;
            padding: 2px;
        }}
        
        QPushButton:hover {{
            background-color: rgba(255, 255, 255, 0.1);
            color: {COPILOT_COLORS['text_primary']};
        }}
        """

def get_input_area_style():
    """Get input area stylesheet"""
    return f"""
    QFrame {{
        background-color: {COPILOT_COLORS['bg_secondary']};
        border-top: 1px solid {COPILOT_COLORS['border_primary']};
    }}
    
    QTextEdit {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        color: {COPILOT_COLORS['text_primary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 8px;
        padding: 10px 12px;
        font-size: 13px;
        line-height: 1.4;
        selection-background-color: {COPILOT_COLORS['accent_blue']};
        selection-color: white;
    }}
    
    QTextEdit:focus {{
        border-color: {COPILOT_COLORS['border_focus']};
        outline: none;
    }}
    
    QTextEdit:hover:!focus {{
        border-color: {COPILOT_COLORS['border_hover']};
    }}
    """

def get_scrollbar_style():
    """Get custom scrollbar stylesheet"""
    return f"""
    QScrollBar:vertical {{
        background-color: {COPILOT_COLORS['bg_primary']};
        width: 6px;
        border-radius: 3px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COPILOT_COLORS['border_primary']};
        border-radius: 3px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COPILOT_COLORS['border_hover']};
    }}
    
    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {{
        border: none;
        background: none;
    }}
    
    QScrollBar:horizontal {{
        background-color: {COPILOT_COLORS['bg_primary']};
        height: 6px;
        border-radius: 3px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {COPILOT_COLORS['border_primary']};
        border-radius: 3px;
        min-width: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {COPILOT_COLORS['border_hover']};
    }}
    
    QScrollBar::add-line:horizontal,
    QScrollBar::sub-line:horizontal {{
        border: none;
        background: none;
    }}
    """

def get_settings_dialog_style():
    """Get settings dialog stylesheet"""
    return f"""
    QDialog {{
        background-color: {COPILOT_COLORS['bg_primary']};
        color: {COPILOT_COLORS['text_primary']};
    }}
    
    QTabWidget::pane {{
        border: 1px solid {COPILOT_COLORS['border_primary']};
        background-color: {COPILOT_COLORS['bg_secondary']};
        border-radius: 6px;
    }}
    
    QTabBar::tab {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        color: {COPILOT_COLORS['text_secondary']};
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        padding: 8px 16px;
        margin-right: 2px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {COPILOT_COLORS['bg_secondary']};
        color: {COPILOT_COLORS['text_primary']};
        border-bottom: 2px solid {COPILOT_COLORS['accent_blue']};
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {COPILOT_COLORS['bg_hover']};
    }}
    
    QGroupBox {{
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 6px;
        margin-top: 1.5em;
        padding-top: 0.5em;
        color: {COPILOT_COLORS['text_primary']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top left;
        left: 10px;
        padding: 0 5px;
        color: {COPILOT_COLORS['text_secondary']};
    }}
    
    QCheckBox {{
        color: {COPILOT_COLORS['text_primary']};
        spacing: 8px;
    }}
    
    QCheckBox::indicator {{
        width: 16px;
        height: 16px;
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 3px;
        background-color: {COPILOT_COLORS['bg_tertiary']};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {COPILOT_COLORS['accent_blue']};
        border-color: {COPILOT_COLORS['accent_blue']};
    }}
    
    QRadioButton {{
        color: {COPILOT_COLORS['text_primary']};
        spacing: 8px;
    }}
    
    QRadioButton::indicator {{
        width: 16px;
        height: 16px;
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 8px;
        background-color: {COPILOT_COLORS['bg_tertiary']};
    }}
    
    QRadioButton::indicator:checked {{
        background-color: {COPILOT_COLORS['accent_blue']};
        border-color: {COPILOT_COLORS['accent_blue']};
    }}
    """

def get_thinking_display_style():
    """Get thinking display stylesheet"""
    return f"""
    QFrame {{
        background-color: {COPILOT_COLORS['bg_tertiary']};
        border: 1px solid {COPILOT_COLORS['border_primary']};
        border-radius: 8px;
    }}
    
    QLabel {{
        color: {COPILOT_COLORS['text_secondary']};
    }}
    
    QProgressBar {{
        border: none;
        background-color: {COPILOT_COLORS['border_primary']};
        border-radius: 1px;
        height: 3px;
    }}
    
    QProgressBar::chunk {{
        background-color: {COPILOT_COLORS['accent_blue']};
        border-radius: 1px;
    }}
    """
