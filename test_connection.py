#!/usr/bin/env python3
"""
Test script to verify server-client connection and API functionality
"""

import requests
import json
import time
import sys
from pathlib import Path

# Configuration
SERVER_URL = "http://127.0.0.1:27027"
TIMEOUT = 10

def test_health_check():
    """Test server health check"""
    print("🔍 Testing server health check...")
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=TIMEOUT)
        response.raise_for_status()
        result = response.json()
        print(f"✅ Health check successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_get_models():
    """Test getting available models"""
    print("\n🔍 Testing model list retrieval...")
    try:
        response = requests.get(f"{SERVER_URL}/api/models/available", timeout=TIMEOUT)
        response.raise_for_status()
        result = response.json()
        print(f"✅ Models retrieved successfully:")
        if "models" in result:
            for model in result["models"]:
                name = model.get("name", "Unknown")
                model_type = model.get("type", "unknown")
                recommended = " (Recommended)" if model.get("recommended") else ""
                print(f"   - {name} ({model_type}){recommended}")
        return True
    except Exception as e:
        print(f"❌ Model retrieval failed: {e}")
        return False

def test_chat_message():
    """Test sending a chat message"""
    print("\n🔍 Testing chat message...")
    try:
        payload = {
            "message": "Hello, this is a test message. Please respond briefly.",
            "conversation_id": "test_conversation",
            "context": {}
        }
        response = requests.post(f"{SERVER_URL}/api/chat/message", json=payload, timeout=30)
        response.raise_for_status()
        result = response.json()

        if result.get("success"):
            print(f"✅ Chat message successful")
            print(f"   Response: {result.get('response', 'No response')[:100]}...")
            return True
        else:
            print(f"✅ Chat message received response")
            print(f"   Response: {str(result)[:100]}...")
            return True
    except Exception as e:
        print(f"❌ Chat message failed: {e}")
        return False

def test_server_info():
    """Test getting server information"""
    print("\n🔍 Testing server info...")
    try:
        response = requests.get(f"{SERVER_URL}/api", timeout=TIMEOUT)
        response.raise_for_status()
        result = response.json()
        print(f"✅ Server info retrieved:")
        print(f"   Name: {result.get('name', 'Unknown')}")
        print(f"   Version: {result.get('version', 'Unknown')}")
        print(f"   Status: {result.get('status', 'Unknown')}")
        return True
    except Exception as e:
        print(f"❌ Server info failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting RCS Server-Client Connection Tests")
    print("=" * 50)
    
    tests = [
        test_health_check,
        test_server_info,
        test_get_models,
        test_chat_message
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Server and API are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check server configuration and status.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
