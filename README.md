# Reverie Code Studio (RCS)

A modern, AI-powered code editor inspired by <PERSON>ursor and Augment Agent, built with Python and PyQt6. Features autonomous AI agents, intelligent code assistance, and a sleek Cursor-style interface.

## ✨ Features

### 🤖 AI Assistant (Cursor-Style)
- **💬 Chat Mode**: Natural conversation with AI about code and programming
- **🤖 Agent Mode**: Autonomous AI agent that can understand and modify your entire codebase
- **✨ Composer Mode**: AI-powered code generation and completion

### 📝 Modern Code Editor
- Syntax highlighting for 20+ programming languages
- File explorer with intelligent project management
- Cursor-inspired dark theme with modern UI
- Real-time AI assistance and suggestions

### 🔧 Advanced AI Features
- **Local AI Models**: Qwen 1.5 series (4B, 7B, 14B parameters)
- **Intelligent Context**: Automatic codebase understanding
- **Multi-step Reasoning**: Complex task completion
- **WebSocket Communication**: Real-time AI streaming

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ 
- 8GB+ RAM (for local AI models)
- Git

### One-Click Setup
```bash
# Clone and start everything
git clone <repository-url>
cd RCS
start_rcs.bat  # Windows
```

### Manual Setup

1. **Start the Server**
   ```bash
   cd server
   pip install -r requirements.txt
   python main.py
   ```

2. **Start the Client** (new terminal)
   ```bash
   cd client
   pip install -r requirements.txt
   python main.py
   ```

3. **Test Connection**
   ```bash
   python test_connection.py
   ```

## 🎯 AI Models

### Default Models (Qwen Series)
- **🚀 Qwen/Qwen1.5-4B** (Recommended) - Fast and efficient
- **⚡ Qwen/Qwen1.5-7B** - Balanced performance  
- **🔥 Qwen/Qwen1.5-14B** - Maximum capability

### Model Features
- **32K Context Length** - Understand large codebases
- **Code + Chat Optimized** - Excellent for programming tasks
- **Multilingual Support** - Works with any programming language
- **Auto-Download** - Models download automatically on first use

## 💡 Usage Guide

### Chat Mode 💬
Perfect for quick questions and code discussions:
```
You: "How do I implement a binary search in Python?"
AI: "Here's an efficient binary search implementation..."
```

### Agent Mode 🤖
For complex, multi-step tasks:
```
You: "Refactor this class to use dependency injection and add unit tests"
AI: [Analyzes code] → [Plans changes] → [Implements refactoring] → [Adds tests]
```

### Composer Mode ✨
For code generation and completion:
```
You: "Create a REST API endpoint for user authentication"
AI: [Generates complete endpoint with validation, error handling, and documentation]
```

## ⚙️ Configuration

### Server Settings (`server/config.json`)
```json
{
  "ai": {
    "default_model": "Qwen/Qwen1.5-4B",
    "temperature": 0.7,
    "max_length": 32768
  }
}
```

### Client Settings (`client/config.json`)
```json
{
  "ai": {
    "default_model": "Qwen/Qwen1.5-4B",
    "stream_responses": true,
    "auto_context": true
  }
}
```

## 🏗️ Architecture

```
RCS/
├── client/              # PyQt6 Cursor-style UI
│   ├── ui/             # Modern interface components
│   ├── api/            # Server communication
│   └── utils/          # Utilities and config
├── server/             # FastAPI AI backend
│   ├── app/api/        # REST API endpoints
│   ├── app/services/   # AI model management
│   └── app/core/       # Core configuration
└── test_connection.py  # Connection testing
```

## 🎨 UI Design

Inspired by Cursor's modern interface:
- **Dark Theme** - Easy on the eyes
- **Clean Typography** - Segoe UI font family
- **Intuitive Icons** - Emoji-enhanced navigation
- **Responsive Layout** - Adapts to screen size
- **Smooth Animations** - Polished interactions

## 🔧 Development

### Adding New Models
1. Update `server/app/services/model_manager.py`
2. Add model configuration to `server/config.json`
3. Test with `python test_connection.py`

### Customizing UI
1. Modify styles in `client/ui/copilot_styles.py`
2. Update components in `client/ui/ai_sidebar.py`
3. Test changes with `python client/main.py`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- **Cursor** - UI/UX inspiration and design patterns
- **Augment Agent** - AI agent architecture and capabilities  
- **Qwen Team** - Excellent open-source language models
- **PyQt6** - Powerful desktop application framework
- **FastAPI** - Modern, fast web framework for APIs
